"""Workflow state store for managing execution state across workers."""

import asyncio
import logging
import uuid
from abc import ABC, abstractmethod
from typing import Literal

from gen_os_sdk_emulator.core.database.session import SessionManager
from sqlalchemy import delete, select, update
from sqlalchemy.sql import func

from gen_os_am_core.database.models.workflows import (
    StateTypeContext,
    StateTypeEvent,
    WorkflowExecutionState,
)
from gen_os_am_core.settings import DatabaseSettings

logger = logging.getLogger(__name__)

db_settings = DatabaseSettings.get_settings()


class WorkflowStateStore(ABC):
    """Abstract base class for workflow state storage."""

    @abstractmethod
    async def set_event(self, execution_id: uuid.UUID) -> None:
        """Set an event for the given execution ID."""
        pass

    @abstractmethod
    async def get_event(self, execution_id: uuid.UUID) -> asyncio.Event | None:
        """Get an event for the given execution ID."""
        pass

    @abstractmethod
    async def clear_event(self, execution_id: uuid.UUID) -> None:
        """Clear an event for the given execution ID."""
        pass

    @abstractmethod
    async def get_events(self) -> set[uuid.UUID]:
        """Get all execution IDs that have events."""
        pass

    @abstractmethod
    async def set_context(self, execution_id: uuid.UUID, context_id: str) -> None:
        """Set context for the given execution ID."""
        pass

    @abstractmethod
    async def get_context(self, execution_id: uuid.UUID) -> str | None:
        """Get context for the given execution ID."""
        pass

    @abstractmethod
    async def get_contexts(self) -> set[uuid.UUID]:
        """Get all execution IDs that have contexts."""
        pass

    @abstractmethod
    async def clear_context(self, execution_id: uuid.UUID) -> None:
        """Clear context for the given execution ID."""
        pass


class InMemoryStateStore(WorkflowStateStore):
    """In-memory state store for single-worker deployments."""

    # Use for development only

    def __init__(self):
        """Initialize the in-memory state store."""
        self.events: dict[uuid.UUID, asyncio.Event] = {}
        self.contexts: dict[uuid.UUID, str] = {}

    async def set_event(self, execution_id: uuid.UUID) -> None:
        """Set an event for the given execution ID."""
        self.events[execution_id] = asyncio.Event()

    async def get_event(self, execution_id: uuid.UUID) -> asyncio.Event | None:
        """Get an event for the given execution ID."""
        return self.events.get(execution_id)

    async def clear_event(self, execution_id: uuid.UUID) -> None:
        """Clear an event for the given execution ID."""
        if execution_id in self.events:
            del self.events[execution_id]

    async def get_events(self) -> set[uuid.UUID]:
        """Get all execution IDs that have events."""
        return set(self.events.keys())

    async def set_context(self, execution_id: uuid.UUID, context_id: str) -> None:
        """Set context for the given execution ID."""
        self.contexts[execution_id] = context_id

    async def get_context(self, execution_id: uuid.UUID) -> str | None:
        """Get context for the given execution ID."""
        return self.contexts.get(execution_id)

    async def get_contexts(self) -> set[uuid.UUID]:
        """Get all execution IDs that have contexts."""
        return set(self.contexts.keys())

    async def clear_context(self, execution_id: uuid.UUID) -> None:
        """Clear context for the given execution ID."""
        if execution_id in self.contexts:
            del self.contexts[execution_id]


class DatabaseStateStore(WorkflowStateStore):
    """Database-based state store for multi-worker deployments."""

    # Use for production

    def __init__(self):
        """Initialize the database state store."""
        # In-memory events for this process (can't store asyncio.Event in DB)
        self._local_events: dict[uuid.UUID, asyncio.Event] = {}

    async def set_event(self, execution_id: uuid.UUID) -> None:
        """Set an event for the given execution ID."""
        async with SessionManager.get_session(db_settings) as db:
            # Store in database that this execution has an event
            state = WorkflowExecutionState(
                execution_id=str(execution_id),
                state_type=StateTypeEvent,
                state_value="waiting",
            )
            db.add(state)
            await db.commit()

        # Create local asyncio.Event for this process
        self._local_events[execution_id] = asyncio.Event()

    async def get_event(self, execution_id: uuid.UUID) -> asyncio.Event | None:
        """Get an event for the given execution ID."""
        async with SessionManager.get_session(db_settings) as db:
            # Check if event exists in database
            result = await db.execute(
                select(WorkflowExecutionState).where(
                    WorkflowExecutionState.execution_id == str(execution_id),
                    WorkflowExecutionState.state_type == StateTypeEvent,
                )
            )
            state = result.scalars().first()

            if not state:
                return None

        # Return or create local event
        if execution_id not in self._local_events:
            self._local_events[execution_id] = asyncio.Event()

        return self._local_events[execution_id]

    async def clear_event(self, execution_id: uuid.UUID) -> None:
        """Clear an event for the given execution ID."""
        async with SessionManager.get_session(db_settings) as db:
            # Remove from database
            await db.execute(
                delete(WorkflowExecutionState).where(
                    WorkflowExecutionState.execution_id == str(execution_id),
                    WorkflowExecutionState.state_type == "event",
                )
            )
            await db.commit()

        # Remove local event
        if execution_id in self._local_events:
            del self._local_events[execution_id]

    async def get_events(self) -> set[uuid.UUID]:
        """Get all execution IDs that have events."""
        async with SessionManager.get_session(db_settings) as db:
            result = await db.execute(
                select(WorkflowExecutionState.execution_id).where(
                    WorkflowExecutionState.state_type == StateTypeEvent
                )
            )
            execution_ids = set()
            for row in result.scalars():
                try:
                    execution_ids.add(uuid.UUID(row))
                except ValueError:
                    logger.warning(f"Invalid execution ID in database: {row}")

            return execution_ids

    async def set_context(self, execution_id: uuid.UUID, context_id: str) -> None:
        """Set context for the given execution ID."""
        async with SessionManager.get_session(db_settings) as db:
            # Try to update existing context
            result = await db.execute(
                update(WorkflowExecutionState)
                .where(
                    WorkflowExecutionState.execution_id == str(execution_id),
                    WorkflowExecutionState.state_type == StateTypeContext,
                )
                .values(state_value=context_id, updated_at=func.now())
            )

            # If no existing context, create new one
            if result.rowcount == 0:
                state = WorkflowExecutionState(
                    execution_id=str(execution_id),
                    state_type=StateTypeContext,
                    state_value=context_id,
                )
                db.add(state)

            await db.commit()

    async def get_context(self, execution_id: uuid.UUID) -> str | None:
        """Get context for the given execution ID."""
        async with SessionManager.get_session(db_settings) as db:
            result = await db.execute(
                select(WorkflowExecutionState.state_value).where(
                    WorkflowExecutionState.execution_id == str(execution_id),
                    WorkflowExecutionState.state_type == StateTypeContext,
                )
            )
            context = result.scalars().first()
            return context

    async def get_contexts(self) -> set[uuid.UUID]:
        """Get all execution IDs that have contexts."""
        async with SessionManager.get_session(db_settings) as db:
            result = await db.execute(
                select(WorkflowExecutionState.execution_id).where(
                    WorkflowExecutionState.state_type == StateTypeContext
                )
            )
            execution_ids = set()
            for row in result.scalars():
                try:
                    execution_ids.add(uuid.UUID(row))
                except ValueError:
                    logger.warning(f"Invalid execution ID in database: {row}")

            return execution_ids

    async def clear_context(self, execution_id: uuid.UUID) -> None:
        """Clear context for the given execution ID."""
        async with SessionManager.get_session(db_settings) as db:
            await db.execute(
                delete(WorkflowExecutionState).where(
                    WorkflowExecutionState.execution_id == str(execution_id),
                    WorkflowExecutionState.state_type == StateTypeContext,
                )
            )
            await db.commit()


def create_state_store(
    state_store_type: Literal["database", "inmemory"],
) -> WorkflowStateStore:
    """Create appropriate state store."""
    if state_store_type == "database":
        logger.info("Using database state store")
        return DatabaseStateStore()
    else:
        logger.info("Using in-memory state store")
        return InMemoryStateStore()
