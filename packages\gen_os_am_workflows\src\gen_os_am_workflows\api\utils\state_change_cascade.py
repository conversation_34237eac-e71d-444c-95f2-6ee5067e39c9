"""State change cascade for interactions."""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import DeclarativeBase

from gen_os_am_workflows.database import (
    Interaction,
    InteractionKindApproved,
    InteractionKindCancelled,
    InteractionKindEdited,
    InteractionKindResumed,
    InteractionKindSolveManually,
    Occurrence,
    OccurrenceReasonAssistanceNeeded,
    OccurrenceReasonError,
    OccurrenceReasonPendingApproval,
    OccurrenceStatusSolved,
    OccurrenceStatusUnsolved,
    StepRun,
    StepRunStatusBlockedAssistanceNeeded,
    StepRunStatusBlockedPendingApproval,
    StepRunStatusCancelled,
    StepRunStatusCompleted,
    StepRunStatusError,
    StepRunStatusRunning,
    WorkflowExecution,
    WorkflowExecutionHistory,
    WorkflowExecutionHistoryEvent,
    WorkflowExecutionHistoryEventApproved,
    WorkflowExecutionHistoryEventBlockedAssistanceNeeded,
    WorkflowExecutionHistoryEventBlockedPendingApproval,
    WorkflowExecutionHistoryEventCancelled,
    WorkflowExecutionHistoryEventCompleted,
    WorkflowExecutionHistoryEventEdited,
    WorkflowExecutionHistoryEventError,
    WorkflowExecutionHistoryEventExecutionStarted,
    WorkflowExecutionHistoryEventResumed,
    WorkflowExecutionStatusBlockedAssistanceNeeded,
    WorkflowExecutionStatusBlockedPendingApproval,
    WorkflowExecutionStatusCancelled,
    WorkflowExecutionStatusCompleted,
    WorkflowExecutionStatusError,
    WorkflowExecutionStatusRunning,
)


class WorkflowExecutionHistoryLogger:
    """A class that logs the workflow execution history."""

    def __init__(self, workflow_execution: WorkflowExecution, db: AsyncSession):
        """Initialize the workflow execution history logger."""
        self.workflow_execution = workflow_execution
        self.db = db

    async def log(
        self,
        event: WorkflowExecutionHistoryEvent,
        termination_message: str | None = None,
        error_message: str | None = None,
        step_name: str | None = None,
    ):
        """Log a workflow execution history event."""
        self.db.add(
            WorkflowExecutionHistory(
                workflow_execution_id=self.workflow_execution.id,
                event=event,
                termination_message=termination_message,
                error_message=error_message,
                step_name=step_name,
            )
        )
        await self.db.commit()


class StateChangeCascade:
    """A class that handles the state change cascade for an entity."""

    def __init__(self, entity: DeclarativeBase, db: AsyncSession):
        """Initialize the state change cascade."""
        self.entity = entity
        self.db = db

    async def _add_and_commit(self, entities: list[DeclarativeBase]):
        """Add and commit an entity."""
        for entity in entities:
            self.db.add(entity)
        await self.db.commit()


class InteractionStateChangeCascade(StateChangeCascade):
    """A class that handles the state change cascade for an interaction."""

    def __init__(self, interaction: Interaction, db: AsyncSession):
        """Initialize the state change cascade."""
        super().__init__(interaction, db)
        self.interaction = interaction

    async def run(self):
        """Run the state change cascade."""
        new_workflow_execution_status = None
        history_event = None

        if self.interaction.kind == InteractionKindApproved:
            # Interaction Approved → Occurrence Solved → Step Run Completed
            # → Workflow Execution Running/Done
            self.interaction.occurrence.status = OccurrenceStatusSolved
            self.interaction.occurrence.step_run.status = StepRunStatusCompleted
            are_all_steps_completed = (
                self.interaction.occurrence.step_run.workflow_execution.are_all_steps_completed()
            )
            new_workflow_execution_status = (
                WorkflowExecutionStatusCompleted
                if are_all_steps_completed
                else WorkflowExecutionStatusRunning
            )
            history_event = (
                WorkflowExecutionHistoryEventApproved
                if are_all_steps_completed
                else WorkflowExecutionHistoryEventResumed
            )

        if self.interaction.kind == InteractionKindResumed:
            # Interaction Approved → Occurrence Solved → Step Run Running
            # → Workflow Execution Running
            self.interaction.occurrence.status = OccurrenceStatusSolved
            self.interaction.occurrence.step_run.status = StepRunStatusRunning
            new_workflow_execution_status = WorkflowExecutionStatusRunning

            history_event = WorkflowExecutionHistoryEventResumed

        if self.interaction.kind == InteractionKindEdited:
            # Interaction Edited → No state change, just flag the StepRun as Edited
            self.interaction.occurrence.step_run.edited = True
            history_event = WorkflowExecutionHistoryEventEdited

        if self.interaction.kind == InteractionKindCancelled:
            # Interaction Cancelled → Occurrence Unsolved → Step Run Cancelled
            # → Workflow Execution Cancelled
            self.interaction.occurrence.status = OccurrenceStatusUnsolved
            self.interaction.occurrence.step_run.status = StepRunStatusCancelled
            new_workflow_execution_status = WorkflowExecutionStatusCancelled
            history_event = WorkflowExecutionHistoryEventCancelled

        if self.interaction.kind == InteractionKindSolveManually:
            # Interaction Solve Manually → Occurrence Solved → Step Run Running
            # → Workflow Execution Running
            self.interaction.occurrence.status = OccurrenceStatusSolved
            self.interaction.occurrence.step_run.status = StepRunStatusRunning
            new_workflow_execution_status = WorkflowExecutionStatusRunning
            history_event = WorkflowExecutionHistoryEventResumed

        await self._add_and_commit(
            [
                self.interaction.occurrence,
                self.interaction.occurrence.step_run,
            ]
        )
        # Workflow Execution needs to be added last otherwise the step run won't update
        # since the Workflow Execution might be in a terminal status.
        if new_workflow_execution_status:
            self.interaction.occurrence.step_run.workflow_execution.status = (
                new_workflow_execution_status
            )
            await self._add_and_commit([self.interaction.occurrence.step_run.workflow_execution])

        if history_event:
            await WorkflowExecutionHistoryLogger(
                self.interaction.occurrence.step_run.workflow_execution, self.db
            ).log(
                event=history_event,
                termination_message=self.interaction.resolution,
                step_name=self.interaction.occurrence.step_run.step.name,
            )


class OccurrenceStateChangeCascade(StateChangeCascade):
    """A class that handles the state change cascade for an occurrence."""

    def __init__(self, occurrence: Occurrence, db: AsyncSession):
        """Initialize the state change cascade."""
        super().__init__(occurrence, db)
        self.occurrence = occurrence

    async def run(self):
        """Run the state change cascade."""
        new_workflow_execution_status = None
        history_event = None
        error_message = None

        if self.occurrence.reason == OccurrenceReasonPendingApproval:
            # Occurrence Pending Approval → Step Run Blocked Pending Approval
            # → Workflow Execution Blocked Pending Approval
            self.occurrence.step_run.status = StepRunStatusBlockedPendingApproval
            new_workflow_execution_status = WorkflowExecutionStatusBlockedPendingApproval
            history_event = WorkflowExecutionHistoryEventBlockedPendingApproval

        if self.occurrence.reason == OccurrenceReasonAssistanceNeeded:
            # Occurrence Assistance Needed → Step Run Blocked Assistance Needed
            # → Workflow Execution Blocked Assistance Needed
            self.occurrence.step_run.status = StepRunStatusBlockedAssistanceNeeded
            new_workflow_execution_status = WorkflowExecutionStatusBlockedAssistanceNeeded
            history_event = WorkflowExecutionHistoryEventBlockedAssistanceNeeded

        if self.occurrence.reason == OccurrenceReasonError:
            # Occurrence Error → Step Run Error
            # → Workflow Execution Error
            self.occurrence.step_run.status = StepRunStatusError
            new_workflow_execution_status = WorkflowExecutionStatusError
            history_event = WorkflowExecutionHistoryEventError
            error_message = self.occurrence.message

        await self._add_and_commit([self.occurrence.step_run])
        # Workflow Execution needs to be added last otherwise the step run won't update
        # since the Workflow Execution might be in a terminal status.
        if new_workflow_execution_status:
            self.occurrence.step_run.workflow_execution.status = new_workflow_execution_status
            await self._add_and_commit([self.occurrence.step_run.workflow_execution])

        if history_event:
            await WorkflowExecutionHistoryLogger(
                self.occurrence.step_run.workflow_execution, self.db
            ).log(
                event=history_event,
                error_message=error_message,
                step_name=self.occurrence.step_run.step.name,
            )


class StepRunStateChangeCascade(StateChangeCascade):
    """A class that handles the state change cascade for a step run."""

    def __init__(self, step_run: StepRun, db: AsyncSession):
        """Initialize the state change cascade."""
        super().__init__(step_run, db)
        self.step_run = step_run

    async def run(self):
        """Run the state change cascade."""
        history_event = None
        if self.step_run.status == StepRunStatusRunning:
            # Step Run Running → Workflow Execution Running
            self.step_run.workflow_execution.status = WorkflowExecutionStatusRunning

            if self.step_run.is_first_step():
                history_event = WorkflowExecutionHistoryEventExecutionStarted

        if self.step_run.status == StepRunStatusCompleted:
            # Step Run Completed → Workflow Execution Running/Done
            are_all_steps_completed = self.step_run.workflow_execution.are_all_steps_completed()
            is_solve_manually = self.step_run.is_solve_manually()

            if is_solve_manually:
                self.step_run.workflow_execution.cancel_uncompleted_step_runs()

            self.step_run.workflow_execution.status = (
                WorkflowExecutionStatusCompleted
                if are_all_steps_completed or is_solve_manually
                else WorkflowExecutionStatusRunning
            )

            history_event = (
                WorkflowExecutionHistoryEventCompleted
                if are_all_steps_completed
                else WorkflowExecutionHistoryEventResumed
            )

        if history_event:
            await WorkflowExecutionHistoryLogger(self.step_run.workflow_execution, self.db).log(
                event=history_event,
                step_name=self.step_run.step.name,
            )

        await self._add_and_commit([self.step_run.workflow_execution])
