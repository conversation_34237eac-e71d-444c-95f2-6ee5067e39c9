"""Pydantic schemas for agent issues management API."""

import uuid
from datetime import datetime
from typing import Any, Literal

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    computed_field,
    field_validator,
    model_validator,
)

from gen_os_am_core.models.enums import IssueCategory, IssueLogType, IssueState

from .common import PaginationDetails, PaginationFilters


class ReportedIncidentConversationBase(BaseModel):
    """Base schema for ReportedIncidentConversation shared fields."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    conversation_id: str = Field(..., description="Conversation identifier")
    answer: str | None = Field(None, description="Answer provided in the conversation")
    description: str | None = Field(
        None, description="Detailed description of the incident"
    )
    severity: str = Field(..., description="Severity level e.g. low, medium, critical")


class ReportedIncidentConversationCreate(ReportedIncidentConversationBase):
    """Schema for creating a ReportedIncidentConversation (client request)."""

    pass


class ReportedIncidentConversationResponse(ReportedIncidentConversationBase):
    """Schema for returning ReportedIncidentConversation data."""

    id: uuid.UUID = Field(..., description="Unique identifier of the incident entry")
    issue_id: uuid.UUID = Field(..., description="Associated Issue identifier")
    created_by: str | None = Field(None, description="User who created this entry")
    created_at: datetime = Field(..., description="Timestamp of creation")


class ReportedIncidentWorkflowBase(BaseModel):
    """Base schema for ReportedIncidentWorkflow shared fields."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    workflow_execution_id: str = Field(..., description="Workflow execution identifier")
    step: str | None = Field(None, description="Step within the workflow")
    description: str | None = Field(
        None, description="Detailed description of the incident"
    )
    severity: str = Field(..., description="Severity level e.g. low, medium, critical")


class ReportedIncidentWorkflowCreate(ReportedIncidentWorkflowBase):
    """Schema for creating a ReportedIncidentWorkflow (client request)."""

    pass


class ReportedIncidentWorkflowResponse(ReportedIncidentWorkflowBase):
    """Schema for returning ReportedIncidentWorkflow data."""

    id: uuid.UUID = Field(..., description="Unique identifier of the incident entry")
    issue_id: uuid.UUID = Field(..., description="Associated Issue identifier")
    created_by: str | None = Field(None, description="User who created this entry")
    created_at: datetime = Field(..., description="Timestamp of creation")


class IssueCreateRequest(BaseModel):
    """Schema for creating a new Issue with related incidents."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    description: str = Field(..., description="Issue description")
    issue_type: IssueCategory = Field(
        ..., description="Predefined symptom/category of the issue"
    )
    state: IssueState = Field(..., description="Initial state of the issue")
    reported_incidents_workflows: list[ReportedIncidentWorkflowCreate] = Field(
        default_factory=list,
        description="Incidents related to workflow executions",
    )
    reported_incidents_conversations: list[ReportedIncidentConversationCreate] = Field(
        default_factory=list,
        description="Incidents related to conversations",
    )


class AddIncidentsRequest(BaseModel):
    """Schema for adding incidents to an existing issue."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    reported_incidents_workflows: list[ReportedIncidentWorkflowCreate] = Field(
        default_factory=list,
        description="Incidents related to workflow executions to add",
    )
    reported_incidents_conversations: list[ReportedIncidentConversationCreate] = Field(
        default_factory=list,
        description="Incidents related to conversations to add",
    )


class IssueResponse(BaseModel):
    """Schema for returning Issue data with nested incidents."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    id: uuid.UUID = Field(..., description="Unique identifier of the issue")
    agent_id: str = Field(..., description="Agent identifier")
    description: str = Field(..., description="Issue description")
    issue_type: IssueCategory = Field(
        ..., description="Predefined symptom/category of the issue"
    )
    state: IssueState = Field(..., description="Current state of the issue")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    reported_incidents_workflows: list[ReportedIncidentWorkflowResponse] = Field(
        default_factory=list,
        description="Incidents related to workflow executions",
    )
    reported_incidents_conversations: list[ReportedIncidentConversationResponse] = (
        Field(
            default_factory=list,
            description="Incidents related to conversations",
        )
    )


class ReportedIncidentConversationUnified(BaseModel):
    """Schema for a conversation incident in the issue detail view."""

    id: uuid.UUID
    kind: Literal["conversation"]
    description: str | None
    severity: str
    created_by: str | None
    created_at: datetime
    conversation_id: str | None = None


class ReportedIncidentWorkflowUnified(BaseModel):
    """Schema for a workflow incident in the issue detail view."""

    id: uuid.UUID
    kind: Literal["workflow"]
    description: str | None
    severity: str
    created_by: str | None
    created_at: datetime
    workflow_execution_id: str | None = None


class IssueWithIncidents(BaseModel):
    """Detailed issue response including all related incidents."""

    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    description: str
    issue_type: IssueCategory
    state: IssueState
    close_desc: str | None = None
    agent: str
    created_at: datetime
    updated_at: datetime
    reported_incidents: list[
        ReportedIncidentConversationUnified | ReportedIncidentWorkflowUnified
    ] = Field(default_factory=list)


class IncidentSoftDeleteRequest(BaseModel):
    """Schema for soft deleting (or restoring) an incident entry."""

    is_deleted: bool = Field(
        ..., description="Whether the incident is deleted (true) or restored (false)"
    )
    deleted_by: str = Field(
        ..., description="Identifier of the user performing the delete/restore action"
    )


class IncidentSoftDeleteResponse(BaseModel):
    """Returned payload after soft deleting/restoring an incident."""

    id: uuid.UUID
    is_deleted: bool
    deleted_at: datetime | None
    deleted_by: str | None
    kind: Literal["conversation", "workflow"]


class IssueUpdateRequest(BaseModel):
    """Schema for updating an issue via PATCH endpoint."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    description: str | None = Field(None, description="Updated issue description")
    issue_type: IssueCategory | None = Field(
        None, description="Updated issue type/symptom"
    )
    state: IssueState | None = Field(None, description="Updated issue state")
    close_desc: str | None = Field(None, description="Description when closing issue")
    deleted: bool | None = Field(None, description="Flag to soft delete the issue")

    @model_validator(mode="after")
    def validate_close_desc_for_closing_state(self):
        """Validate that close_desc is provided when state is set to a closing value."""
        if self.state in ["closed_fixed", "closed_wont_fix"] and not self.close_desc:
            raise ValueError("close_desc is required when closing an issue")
        return self


class IssueUpdateResponse(BaseModel):
    """Schema for returning updated issue data."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    id: uuid.UUID = Field(..., description="Unique identifier of the issue")
    agent_id: str = Field(..., description="Agent identifier")
    description: str = Field(..., description="Issue description")
    issue_type: IssueCategory = Field(..., description="Issue type/symptom")
    state: IssueState = Field(..., description="Current state of the issue")
    close_desc: str | None = Field(None, description="Description when closing issue")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


# Schemas for listing issues (moved from global_issues.py)
class IssueListFilters(PaginationFilters):
    """Query string filters for listing issues."""

    is_open: bool = Field(
        True,
        description="""If true, return only open issues and include summary;
        if false, return only closed issues without summary.""",
    )
    search: str = ""
    search_column: Literal["id", "description", "issue_type", "severity"] | None = None

    @field_validator("sort_field")
    @classmethod
    def _validate_sort_field(cls, v: str) -> str:
        allowed = {"created_at", "updated_at", "incidents"}
        if v not in allowed:
            raise ValueError(
                f"Invalid sort_field. Allowed values are: {', '.join(allowed)}"
            )
        return v


class IssueListItem(BaseModel):
    """Simplified schema for issue rows in list endpoints."""

    id: uuid.UUID
    description: str
    issue_type: IssueCategory
    state: IssueState
    agent_name: str | None = None
    severity: str | None = None
    incidents: int
    created_at: datetime
    updated_at: datetime


class PaginatedIssuesResponse(BaseModel):
    """Standard paginated response for issue list endpoint."""

    items: list[IssueListItem]
    pagination: PaginationDetails
    summary: dict[str, Any] | None = None


# Issue Log Schemas
class IssueLogDetail(BaseModel):
    """Schema for log detail entries (before/after states)."""

    target: str | None = Field(None, description="Target field or object that changed")
    previous_state: str | None = Field(None, description="Previous value/state")
    new_state: str | None = Field(None, description="New value/state")


class IssueLogResponse(BaseModel):
    """Schema for issue log entries in the events timeline."""

    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID = Field(..., description="Unique identifier of the log entry")
    log_type: IssueLogType = Field(..., description="Type of log event (status)")
    comment: str | None = Field(None, description="Optional comment for the log entry")
    details: list[IssueLogDetail] = Field(
        default_factory=list, description="List of detailed changes (before/after)"
    )
    author_name: str | None = Field(None, description="Display name of the user")
    author_id: str | None = Field(None, description="User ID who created this log entry")
    created_at: datetime = Field(..., description="When the log entry was created")
    updated_at: datetime | None = Field(None, description="When the log entry was last updated")

    # New fields for editing and soft deletion
    is_deleted: bool = Field(False, description="Whether this log entry is soft deleted")
    was_edited: bool = Field(False, description="Whether this log entry was edited")
    edited_at: datetime | None = Field(None, description="When the log entry was last edited")
    edited_by: str | None = Field(None, description="User who last edited this log entry")
    deleted_at: datetime | None = Field(None, description="When the log entry was deleted")
    deleted_by: str | None = Field(None, description="User who deleted this log entry")

    @classmethod
    def from_issue_log(cls, issue_log: "IssueLog") -> "IssueLogResponse":
        """Create IssueLogResponse from IssueLog model with proper metadata parsing."""
        metadata = issue_log.log_metadata or {}

        # Extract comment from metadata
        comment = metadata.get("comment")

        # Extract details from metadata
        details_data = metadata.get("details", [])
        details = [
            IssueLogDetail(
                target=detail.get("target"),
                previous_state=detail.get("previousState"),
                new_state=detail.get("newState"),
            )
            for detail in details_data
        ]

        # Handle soft deleted comments
        if issue_log.is_deleted and issue_log.log_type == "commented":
            comment = "This comment was deleted."

        return cls(
            id=issue_log.id,
            log_type=issue_log.log_type,
            comment=comment,
            details=details,
            author_name=issue_log.created_by,  # For now, use created_by as author_name
            author_id=issue_log.created_by,
            created_at=issue_log.created_at,
            updated_at=issue_log.edited_at,
            is_deleted=issue_log.is_deleted,
            was_edited=issue_log.edited_at is not None,
            edited_at=issue_log.edited_at,
            edited_by=issue_log.edited_by,
            deleted_at=issue_log.deleted_at,
            deleted_by=issue_log.deleted_by,
        )


class IssueLogsResponse(BaseModel):
    """Response schema for listing issue logs (events timeline)."""

    logs: list[IssueLogResponse] = Field(
        ..., description="List of log entries for the issue"
    )
    total_count: int = Field(..., description="Total number of log entries")


class IssueLogPatchRequest(BaseModel):
    """Schema for PATCH requests to edit or delete issue log entries."""

    action: Literal["edit", "delete"] = Field(..., description="Action to perform on the log entry")
    new_comment: str | None = Field(None, description="New comment text (required for edit action)")

    @model_validator(mode="after")
    def validate_edit_requires_comment(self):
        """Validate that edit action requires new_comment."""
        if self.action == "edit" and not self.new_comment:
            raise ValueError("new_comment is required when action is 'edit'")
        return self


