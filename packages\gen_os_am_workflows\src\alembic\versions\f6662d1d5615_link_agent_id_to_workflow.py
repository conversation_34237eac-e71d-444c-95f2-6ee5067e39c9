"""Link agent_id to workflow.

Revision ID: f6662d1d5615
Revises: 18ea09623354
Create Date: 2025-07-16 15:17:38.798380

"""

from collections.abc import Sequence

from gen_os_am_core.database.models.agents import Agent
from gen_os_sdk_emulator.core.database.migrations import exist_table

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f6662d1d5615"
down_revision: str | Sequence[str] | None = "18ea09623354"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = "72266d413d36"


def upgrade() -> None:
    """Upgrade schema."""
    # Add foreign key constraint from workflow.agent_id to agents.id
    if exist_table(Agent.__tablename__):
        op.create_foreign_key(
            constraint_name="fk_workflow_agent_id",
            source_table="workflow",
            referent_table="agents",
            local_cols=["agent_id"],
            remote_cols=["id"],
        )


def downgrade() -> None:
    """Downgrade schema."""
    # Remove foreign key constraint
    op.drop_constraint(
        constraint_name="fk_workflow_agent_id",
        table_name="workflow",
        type_="foreignkey",
    )
