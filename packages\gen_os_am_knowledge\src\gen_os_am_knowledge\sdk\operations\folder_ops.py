"""Module for folder operations."""

import logging
import uuid
from copy import deepcopy
from datetime import datetime, timezone

from gen_os_sdk_emulator.core.database.session import SessionManager
from psycopg.errors import UniqueViolation
from pydantic import BaseModel
from sqlalchemy import and_, not_, or_, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import aliased, contains_eager, joinedload
from sqlalchemy.util import topological
from sqlmodel import delete, select

from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.config.settings import DatabaseSettings
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.models import Document, Folder, Ingestion, User
from gen_os_am_knowledge.sdk.operations.user_ops import UserService
from gen_os_am_knowledge.sdk.operations.utils import (
    NOT_PROVIDED,
    DeletionResult,
    UpdateResult,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
    start_time_var,
)

db_settings = DatabaseSettings.get_settings()


class EnrichedFolder(BaseModel):
    """Enriched Folder model with all the contents of the folder."""

    id: uuid.UUID
    name: str
    parent_id: uuid.UUID | None
    documents: list[Document] | None = None
    children: list[Folder] | None = None
    ingestion: Ingestion | None = None


class RecursiveSearchResult(BaseModel):
    """Recursive search result model."""

    folder_ids: list[uuid.UUID]
    documents_ids: list[uuid.UUID]


class FolderService:
    """Service class to handle CRUD operations for Folder model."""

    def __init__(
        self,
        fs_connector: BaseFileSystemConnector,
        logger: logging.Logger | None = None,
    ):
        """Initialize the FolderService.

        Args:
            fs_connector: The file system connector to use for file operations.
            logger: The logger to use for logging operations.

        """
        self.fs_connector = fs_connector
        self.logger = logger or get_sdk_logger()
        self.user_service = UserService(logger=logger)

    @log_decorator(category=["database", "file"])
    async def create_folder(
        self, folder_name: str, user_id: uuid.UUID, parent_id: uuid.UUID | None = None
    ) -> Folder:
        """Create a new folder in the database.

        Args:
            folder_name: The name of the folder to create.
                Cannot contain '/' as it is used to create a single
                folder in the filesystem.
            user_id: The id of the user creating the folder.
            parent_id: The id of the parent folder. Defaults to None.

        Returns:
            Folder: The created folder instance.

        """
        await self._check_user(user_id)
        found_folder = await self.search_folders(
            folder_name=folder_name, parent_id=parent_id
        )
        if found_folder:
            if found_folder[0].deleted_date is None:
                raise ValueError(f"Folder with name {folder_name} already exists")
            else:
                result = await self.restore_folder(
                    folder_id=found_folder[0].id, user_id=user_id
                )
                if not result.success:
                    raise Exception(result.error)
                else:
                    return result.updated_instance
        else:
            async with SessionManager.get_session(db_settings) as session:
                folder = Folder(
                    name=folder_name,
                    parent_id=parent_id,
                    created_by_id=user_id,
                    last_modified_by_id=user_id,
                )
                try:
                    session.add(folder)
                    await session.commit()
                    await session.refresh(folder)
                except IntegrityError as e:
                    if isinstance(e.orig, UniqueViolation):
                        raise ValueError(
                            f"Folder with name {folder_name} already exists"
                        ) from e
                    raise e
                except Exception as e:
                    raise e

                if parent_id:
                    folder_path = (await self.get_folder_path(parent_id)) + folder_name
                else:
                    folder_path = folder_name

                try:
                    self.fs_connector.create_folder(folder_path)
                    return folder
                except Exception as e:
                    query = delete(Folder).where(Folder.id == folder.id)
                    await session.execute(query)
                    await session.commit()
                    raise e

    @log_decorator()
    async def delete_folder(
        self, folder_id: uuid.UUID, user_id: uuid.UUID
    ) -> DeletionResult:
        """Delete a folder from the database based on folder_id.

        User_id is required to update the last_modified_by_id field.

        Note! This operation is soft delete, the folder is not removed from the storage.

        Args:
            folder_id: The ID of the folder to delete.
            user_id: The ID of the user deleting the folder.

        Returns:
            DeletionResult: The result of the deletion operation.

        """
        await self._check_user(user_id)
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = select(Folder).where(Folder.id == folder_id)
            deleted_folder = deepcopy(
                (await session.execute(query)).scalar_one_or_none()
            )
        if not deleted_folder:
            msg = f"Folder with id {folder_id} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            return DeletionResult(success=False, error=msg)
        else:
            result = await self.recursive_folder_tree(folder_id=folder_id)
            if result.documents_ids:
                msg = (
                    f"Folder {deleted_folder.name} has documents in it. Cannot delete."
                )
                raise ValueError(msg)
            if result.folder_ids:
                for folder_id in result.folder_ids[::-1]:
                    deleted_folder = await self._delete_folder(
                        folder_id=folder_id, user_id=user_id
                    )

                return DeletionResult(success=True, deleted_instance=deleted_folder)

    async def restore_folder(
        self, folder_id: uuid.UUID, user_id: uuid.UUID
    ) -> UpdateResult:
        """Restore a folder from the database based on folder_id.

        User_id is required to update the last_modified_by_id field.

        Args:
            folder_id: The ID of the folder to restore.
            user_id: The ID of the user restoring the folder.

        Returns:
            UpdateResult: The result of the restoration operation.

        """
        await self._check_user(user_id)
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = select(Folder).where(Folder.id == folder_id)
            restored_folder = (await session.execute(query)).scalar_one_or_none()
            if not restored_folder:
                msg = f"Folder with id {folder_id} not found"
                error_event_log(
                    msg=msg,
                    start=start,
                    error=ValueError(msg),
                    logger=self.logger,
                )
                return UpdateResult(success=False, error=msg)
            else:
                updated_values = {
                    "deleted_date": (restored_folder.deleted_date, None),
                }
                restored_folder.deleted_date = None
                restored_folder.last_modified_by_id = user_id
                session.add(restored_folder)
                restored_folder = restored_folder.model_copy()
                await session.commit()

                return UpdateResult(
                    success=True,
                    updated_instance=restored_folder,
                    updated_values=updated_values,
                )

    @log_decorator(type_="access")
    async def get_folder(
        self,
        folder_id: uuid.UUID,
        include_dependencies: bool = False,
        include_published_and_deleted: bool = False,
        include_deleted_folders: bool = False,
    ) -> EnrichedFolder | None:
        """Retrieve folder from the database based on folder id.

        Args:
            folder_id: The id of the folder to retrieve.
            include_dependencies: bool = False - If True, includes all the
                contents of the folder.
            include_published_and_deleted: bool = False - If True, includes
                documents that are in published and deleted. Only valid if
                include_dependencies is True.
            include_deleted_folders: bool = False - If True, includes children
                folders that are deleted. Only valid if include_dependencies is True.

        Returns:
            EnrichedFolder | None: The folder instance with all the contents and
                ingestion that uses that folder as root or None if not found.

        """
        async with SessionManager.get_session(db_settings) as session:
            if include_dependencies:
                query = (
                    select(Folder)
                    .where(Folder.id == folder_id)
                    .options(joinedload(Folder.ingestion))
                )
                if include_published_and_deleted:
                    query = query.options(joinedload(Folder.documents))
                else:
                    doc = aliased(Folder.documents.prop.mapper.class_)
                    query = query.outerjoin(
                        doc,
                        and_(
                            Folder.id == doc.folder_id,
                            or_(doc.deleted_date == None, doc.draft),  # noqa: E711 Comparison to `None` should be `cond is None`
                        ),
                    ).options(contains_eager(Folder.documents, alias=doc))
                if include_deleted_folders:
                    query = query.options(
                        joinedload(Folder.children),
                    )
                else:
                    fold = aliased(Folder.children.prop.mapper.class_)
                    query = query.outerjoin(
                        fold,
                        and_(Folder.id == fold.parent_id, fold.deleted_date == None),  # noqa: E711 Comparison to `None` should be `cond is None`
                    ).options(
                        contains_eager(Folder.children, alias=fold),
                    )

                folder = (await session.execute(query)).scalars().first()
                if folder:
                    return EnrichedFolder(
                        id=folder.id,
                        name=folder.name,
                        parent_id=folder.parent_id,
                        documents=folder.documents,
                        children=folder.children,
                        ingestion=folder.ingestion,
                    )
                else:
                    return None
            else:
                query = select(Folder).where(Folder.id == folder_id)
                folder = (await session.execute(query)).scalars().first()
                if folder:
                    return EnrichedFolder(
                        id=folder.id,
                        name=folder.name,
                        parent_id=folder.parent_id,
                    )
                else:
                    return None

    @log_decorator(type_="access")
    async def search_folders(
        self,
        folder_name: str | None = None,
        parent_id: uuid.UUID | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Folder]:
        """Retrieve folders from the database.

        Args:
            folder_name: str = None - The folder name to search for.
            parent_id: uuid.UUID | None = NOT_PROVIDED - The id of the parent
                folder. Pass None to search for root folders.
            limit: int = 20 - The number of folders to retrieve.
            offset: int = 0 - The offset to start retrieving folders from.

        Returns:
            list[Folder]: The list of folders retrieved.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Folder)

            if folder_name:
                query = query.where(Folder.name == folder_name)
            if parent_id is not NOT_PROVIDED:
                query = query.where(Folder.parent_id == parent_id)

            query = query.limit(limit).offset(offset)
            return (await session.execute(query)).scalars().all()

    @log_decorator(category=["database", "file"])
    async def update_folder(
        self,
        folder_id: uuid.UUID,
        user_id: uuid.UUID,
        folder_name: str | type[NOT_PROVIDED] = NOT_PROVIDED,
        parent_folder_id: uuid.UUID | None | type[NOT_PROVIDED] = NOT_PROVIDED,
    ) -> UpdateResult:
        """Update a folder in the database.

        Args:
            folder_id: The id of the folder to update. Only required argument.
            folder_name: str - The new name of the folder.
            user_id: uuid.UUID - The id of the user updating the folder.
            parent_folder_id: uuid.UUID | None - The new parent folder id.
                Pass None to update the folder to a root folder.

        Returns:
            UpdateResult: The result of the update operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = select(Folder).where(Folder.id == folder_id)
            folder = (await session.execute(query)).scalar_one_or_none()
            old_folder = folder.model_copy()
            if not folder:
                error_event_log(
                    msg=f"Folder with id {folder_id} not found.",
                    start=start,
                    error=Exception(f"Folder with id {folder_id} not found."),
                    logger=self.logger,
                )
                return UpdateResult(
                    success=False, error=f"Folder with id {folder_id} not found."
                )
            else:
                folder_path = await self.get_folder_path(folder_id)
                update_dict = {
                    "name": folder_name,
                    "parent_id": parent_folder_id,
                    "last_modified_by_id": user_id,
                }
                updated_values = {}
                for k, v in update_dict.items():
                    if v is not NOT_PROVIDED:
                        if getattr(folder, k) != v:
                            updated_values[k] = (getattr(folder, k), v)
                            setattr(folder, k, v)
                new_folder = folder.model_copy()
                try:
                    session.add(folder)
                    await session.commit()
                except IntegrityError as e:
                    if isinstance(e.orig, UniqueViolation):
                        raise ValueError(
                            f"Folder with name {new_folder.name} already exists"
                        ) from e
                    raise e
                except Exception as e:
                    raise e

        try:
            if folder_name is not NOT_PROVIDED or parent_folder_id is not NOT_PROVIDED:
                if (
                    folder_name is not NOT_PROVIDED
                    and parent_folder_id is not NOT_PROVIDED
                ):
                    if parent_folder_id is None:
                        new_folder_path = folder_name
                    else:
                        new_folder_path = (
                            await self.get_folder_path(parent_folder_id) + folder_name
                        )
                elif folder_name is not NOT_PROVIDED:
                    head, _, _ = folder_path.rstrip("/").rpartition("/")
                    new_folder_path = (head + "/" if head else "") + folder_name
                elif parent_folder_id is not NOT_PROVIDED:
                    if parent_folder_id is None:
                        new_folder_path = old_folder.name
                    else:
                        new_folder_path = (
                            await self.get_folder_path(parent_folder_id)
                            + old_folder.name
                        )

                self.fs_connector.move_folder(
                    folder_path, new_folder_path, with_folder=False
                )

            return UpdateResult(
                success=True, updated_instance=new_folder, updated_values=updated_values
            )
        except Exception as e:
            async with SessionManager.get_session(db_settings) as session:
                query = select(Folder).where(Folder.id == old_folder.id)
                folder = (await session.execute(query)).scalar_one_or_none()
                for k, v in old_folder.model_dump().items():
                    setattr(folder, k, v)
                session.add(folder)
                await session.commit()
                raise e

    @log_decorator(type_="access")
    async def get_folder_path(self, folder_id: uuid.UUID) -> str | None:
        """Get the full path of a folder.

        Args:
            folder_id: The id of the folder.

        Returns:
            The full path of the folder or None if the folder does not exist.

        """
        folder = await self.get_folder(folder_id)
        if not folder:
            return None
        path = folder.name
        while folder.parent_id:
            folder = await self.get_folder(folder.parent_id)
            path = f"{folder.name}/{path}"
        return path + "/"

    @log_decorator(category=["database", "file"])
    async def create_folders_from_path(
        self, path: str, user_id: uuid.UUID
    ) -> list[Folder]:
        """Create folders from a given path.

        If the folders already exist, it will not create them again.

        Args:
            path: The path to create folders from.
                The path should full, starting from the root folder.
                The format of the path `folder1/folder2/folder3`
            user_id: The ID of the user creating the folders.

        Returns:
            list[Folder]: The list of all the folders on the path:
                created or already existing.

        """
        await self._check_user(user_id)
        created_folders = []
        for cnt, folder_name in enumerate(path.rstrip("/").split("/")):
            if cnt == 0:
                folders = await self.search_folders(
                    folder_name=folder_name, parent_id=None
                )
            else:
                folders = await self.search_folders(
                    folder_name=folder_name, parent_id=parent_id
                )

            if not folders:
                if cnt == 0:
                    created_folder = await self.create_folder(
                        folder_name, user_id=user_id
                    )
                    parent_id = created_folder.id
                else:
                    created_folder = await self.create_folder(
                        folder_name, parent_id=parent_id, user_id=user_id
                    )
                    parent_id = created_folder.id
                created_folders.append(created_folder)
            else:
                parent_id = folders[0].id
                created_folders.append(folders[0])

        return created_folders

    @log_decorator(category=["database", "file"])
    async def cleanup_deleted_folders(self) -> tuple[list[Folder], list[Folder]]:
        """Cleanup all folders from the database deleted_date older than current date.

        Returns:
            tuple[list[Folder], list[Folder]]: A tuple containing two lists:
                - deleted_folders: The list of folders that were deleted.
                - not_deleted_folders: The list of folders that were not deleted.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Folder).where(Folder.deleted_date < datetime.now())
            folders = (await session.execute(query)).scalars().all()
            folders_dict = {i.id: i for i in folders}
            # topological sort to delete folders in the right order
            pairs = [(i.parent_id, i.id) for i in folders if i.parent_id]
            allitems = [i.id for i in folders]
            folders_topological = list(topological.sort(pairs, allitems))[::-1]
            deleted_folders = []
            not_deleted_folders = []
            for folder_uuid in folders_topological:
                folder = folders_dict[folder_uuid]
                start = datetime.now(timezone.utc)
                try:
                    self.fs_connector.delete_folder(
                        await self.get_folder_path(folder_uuid)
                    )
                    stmt = delete(Folder).where(Folder.id == folder.id)
                    deleted_folder = (
                        await session.execute(stmt.returning(Folder))
                    ).scalar_one_or_none()
                    deleted_folders.append(deleted_folder[0].model_copy())
                except Exception as e:
                    not_deleted_folders.append(folder)
                    msg = f"Folder with id {folder.id} not deleted. Error: {e}"
                    error_event_log(
                        msg=msg,
                        start=start,
                        error=ValueError(msg),
                        logger=self.logger,
                    )

            await session.commit()

            return (deleted_folders, not_deleted_folders)

    async def _check_user(self, user_id: uuid.UUID) -> User:
        """Check if the user exists in the database.

        Args:
            user_id (uuid.UUID): The ID of the user.

        Returns:
            User: The user object if found.
            Raises ValueError if the user is not found.

        """
        start = datetime.now(timezone.utc)
        user = await self.user_service.get_user(user_id)
        if not user:
            msg = f"User with id {user_id} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            raise ValueError(msg)
        return user

    async def recursive_folder_tree(
        self,
        folder_id: uuid.UUID,
        include_published_and_deleted: bool = False,
        include_deleted_folders: bool = False,
    ) -> RecursiveSearchResult:
        """Get a recursive folder tree with all subfolders and documents.

        Args:
            folder_id: The ID of the root folder to start the tree from.
            include_published_and_deleted: Whether to include published and
                deleted documents.
            include_deleted_folders: Whether to include deleted folders in the tree.

        Returns:
            RecursiveSearchResult: Contains lists of folder IDs and document IDs
                in the tree.

        """
        async with SessionManager.get_session(db_settings) as session:
            n1 = aliased(Folder)
            n2 = aliased(Folder)

            base = select(n1.id, n1.parent_id).where(n1.id == folder_id)
            if not include_deleted_folders:
                base = base.where(n1.deleted_date.is_(None))

            tree_cte = base.cte(name="tree", recursive=True)
            rec = select(n2.id, n2.parent_id).join(
                tree_cte, n2.parent_id == tree_cte.c.id
            )
            if not include_deleted_folders:
                rec = rec.where(n2.deleted_date.is_(None))

            tree_cte = tree_cte.union_all(rec)
            tree = select(tree_cte.c.id)

            result = await session.execute(tree)
            folder_ids = deepcopy(result.scalars().all())

            query = select(Document.id).where(Document.folder_id.in_(folder_ids))
            if not include_published_and_deleted:
                query = query.where(
                    not_(
                        and_(
                            Document.deleted_date.is_not(None),
                            Document.draft.is_(False),
                        )
                    )
                )
            documents_ids = deepcopy((await session.execute(query)).scalars().all())
            return RecursiveSearchResult(
                folder_ids=folder_ids, documents_ids=documents_ids
            )

    async def _delete_folder(
        self, folder_id: uuid.UUID, user_id: uuid.UUID | None = None
    ):
        """Delete a folder from db.

        User_id is required to update the last_modified_by_id field.

        Args:
            folder_id: The folder to delete.
            user_id: The id of the user deleting the folder. Defaults to None.

        Returns:
            Folder: The deleted folder instance.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Folder).where(Folder.id == folder_id)
            folder = (await session.execute(query)).scalar_one_or_none()
            if not folder:
                raise ValueError(f"Folder with id {folder_id} not found")
            folder.deleted_date = datetime.now()
            folder.last_modified_by_id = user_id
            session.add(folder)
            folder = folder.model_copy()
            await session.commit()

            return folder
