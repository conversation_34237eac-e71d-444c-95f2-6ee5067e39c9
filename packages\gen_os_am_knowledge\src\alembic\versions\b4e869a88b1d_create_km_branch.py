"""create km branch.

Revision ID: b4e869a88b1d
Revises:
Create Date: 2025-07-16 14:11:14.168442

"""

from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "b4e869a88b1d"
down_revision: str | Sequence[str] | None = "9586ab310865"
branch_labels: str | Sequence[str] | None = ("km",)
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
