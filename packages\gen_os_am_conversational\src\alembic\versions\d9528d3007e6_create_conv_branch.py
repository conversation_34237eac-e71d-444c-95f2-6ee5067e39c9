"""create conv branch.

Revision ID: d9528d3007e6
Revises:
Create Date: 2025-07-16 14:11:33.325293

"""

from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "d9528d3007e6"
down_revision: str | Sequence[str] | None = "e0f5970bdd94"
branch_labels: str | Sequence[str] | None = ("conv",)
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
