"""Tests for the telemetry router and integration tests."""

import uuid
from datetime import datetime, timezone

import pytest
from opentelemetry.proto.collector.trace.v1.trace_service_pb2 import (
    ExportTraceServiceRequest,
)
from opentelemetry.proto.trace.v1.trace_pb2 import (
    ResourceSpans,
    ScopeSpans,
)
from opentelemetry.proto.trace.v1.trace_pb2 import (
    Span as SpanProto,
)


class TestTelemetryRouter:
    """Test cases for the telemetry router."""

    @pytest.mark.asyncio
    async def test_receive_otel_empty_request(self, client):
        """Test OTLP reception with empty request."""
        # Create empty OTLP request
        otlp_request = ExportTraceServiceRequest()

        response = client.post(
            "/otel/traces",
            content=otlp_request.SerializeToString(),
            headers={"Content-Type": "application/x-protobuf"},
        )
        assert response.status_code == 200
        assert response.json() == {"status": "ok"}

    @pytest.mark.asyncio
    async def test_receive_otel_invalid_data(self, client):
        """Test OTLP reception with invalid data."""
        # Send invalid protobuf data
        response = client.post(
            "/otel/traces",
            content=b"invalid protobuf data",
            headers={"Content-Type": "application/x-protobuf"},
        )
        # Should return 400 Bad Request for invalid protobuf data
        assert response.status_code == 400
        assert "Invalid protobuf data" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_receive_otel_simple_span(self, client):
        """Test OTLP reception with a simple span."""
        # Create a simple OTLP request with minimal data
        otlp_request = ExportTraceServiceRequest()
        resource_span = ResourceSpans()
        scope_span = ScopeSpans()

        # Create a simple span
        span = SpanProto()
        span.trace_id = uuid.uuid4().bytes
        span.span_id = uuid.uuid4().bytes
        span.name = "test.span"
        span.start_time_unix_nano = int(datetime.now(timezone.utc).timestamp() * 1e9)
        span.end_time_unix_nano = int(datetime.now(timezone.utc).timestamp() * 1e9)

        scope_span.spans.append(span)
        resource_span.scope_spans.append(scope_span)
        otlp_request.resource_spans.append(resource_span)

        response = client.post(
            "/otel/traces",
            content=otlp_request.SerializeToString(),
            headers={"Content-Type": "application/x-protobuf"},
        )
        assert response.status_code == 200
        assert response.json() == {"status": "ok"}

    @pytest.mark.asyncio
    async def test_receive_otel_conversation_span(self, client):
        """Test OTLP conversation span processing."""
        # Create OTLP request with conversation span
        otlp_request = ExportTraceServiceRequest()
        resource_span = ResourceSpans()
        scope_span = ScopeSpans()

        # Create a conversation span
        span = SpanProto()
        span.trace_id = uuid.uuid4().bytes
        span.span_id = uuid.uuid4().bytes
        span.name = "gen_os.activity.conversation"
        span.start_time_unix_nano = int(datetime.now(timezone.utc).timestamp() * 1e9)
        span.end_time_unix_nano = int(datetime.now(timezone.utc).timestamp() * 1e9)

        # Add conversation attributes
        attr1 = span.attributes.add()
        attr1.key = "conversation.id"
        attr1.value.string_value = str(uuid.uuid4())

        attr2 = span.attributes.add()
        attr2.key = "agent.id"
        attr2.value.string_value = "test-agent"

        attr3 = span.attributes.add()
        attr3.key = "message.role"
        attr3.value.string_value = "user"

        attr4 = span.attributes.add()
        attr4.key = "message.content"
        attr4.value.string_value = "Hello, world!"

        scope_span.spans.append(span)
        resource_span.scope_spans.append(scope_span)
        otlp_request.resource_spans.append(resource_span)

        response = client.post(
            "/otel/traces",
            content=otlp_request.SerializeToString(),
            headers={"Content-Type": "application/x-protobuf"},
        )
        assert response.status_code == 200
        assert response.json() == {"status": "ok"}

    @pytest.mark.asyncio
    async def test_telemetry_endpoint_exists(self, client):
        """Test that the telemetry endpoint exists and responds."""
        # Test with a simple GET request to see if the endpoint exists
        response = client.get("/otel/traces")
        # Should return 405 Method Not Allowed since it's a POST endpoint
        assert response.status_code == 405


class TestRouterIntegration:
    """Integration tests for router functionality."""

    @pytest.mark.asyncio
    async def test_conversation_lifecycle(self, client):
        """Test complete conversation lifecycle through telemetry and API."""
        agent_id = "integration-test-agent"

        # 1. Try to create conversation through telemetry (optional)
        conversation_id = str(uuid.uuid4())
        trace_id = uuid.uuid4()
        span_id = uuid.uuid4()

        # Create OTLP request with conversation span
        otlp_request = ExportTraceServiceRequest()
        resource_span = ResourceSpans()
        scope_span = ScopeSpans()

        span = SpanProto()
        span.trace_id = trace_id.bytes
        span.span_id = span_id.bytes
        span.name = "gen_os.activity.conversation"
        span.start_time_unix_nano = int(datetime.now(timezone.utc).timestamp() * 1e9)
        span.end_time_unix_nano = int(datetime.now(timezone.utc).timestamp() * 1e9)

        # Add conversation attributes
        attr1 = span.attributes.add()
        attr1.key = "gen_ai.conversation.id"
        attr1.value.string_value = conversation_id

        attr2 = span.attributes.add()
        attr2.key = "gen_ai.agent.id"
        attr2.value.string_value = agent_id

        attr3 = span.attributes.add()
        attr3.key = "gen_ai.conversation.question"
        attr3.value.string_value = "user"

        attr4 = span.attributes.add()
        attr4.key = "gen_ai.conversation.response"
        attr4.value.string_value = "Integration test message"

        scope_span.spans.append(span)
        resource_span.scope_spans.append(scope_span)
        otlp_request.resource_spans.append(resource_span)

        # Send telemetry data
        response = client.post(
            "/otel/traces",
            content=otlp_request.SerializeToString(),
            headers={"Content-Type": "application/x-protobuf"},
        )
        assert response.status_code == 200

        # 2. Verify conversation exists through API
        response = client.get(f"/agents/{agent_id}/conv/v1/conversations")
        assert response.status_code == 200

        data = response.json()
        assert len(data["items"]) == 1

        # Find our conversation
        conversation = None
        for conv in data["items"]:
            if conv["agent_id"] == agent_id:
                conversation = conv
                break

        assert conversation is not None

        # 3. Get conversation metadata
        response = client.get(f"/agents/{agent_id}/conv/v1/conversations/{conversation['id']}")
        assert response.status_code == 200

        # 4. Get conversation messages
        response = client.get(
            f"/agents/{agent_id}/conv/v1/conversations/{conversation['id']}/messages"
        )
        assert response.status_code == 200

        messages_data = response.json()
        # Messages should exist since we created them directly
        assert "items" in messages_data
        assert "pagination" in messages_data
        assert len(messages_data["items"]) >= 1
