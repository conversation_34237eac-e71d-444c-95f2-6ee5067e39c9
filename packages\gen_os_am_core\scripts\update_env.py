"""Update the .env file with the correct values for the specified environment."""

import argparse
from pathlib import Path
from typing import Literal

EnvironmentType = Literal["local", "docker"]


def get_replacements(env_type: EnvironmentType) -> dict[str, str]:
    """Get the replacement values for the specified environment.

    Args:
        env_type: The type of environment to get replacements for.

    Returns:
        A dictionary of key-value pairs to replace in the .env file.

    """
    # Common database configuration
    db_host = "postgres" if env_type == "docker" else "localhost"

    return {
        # Agent Manager database configuration
        "AM_CORE_DATABASE_HOST=": f"AM_CORE_DATABASE_HOST={db_host}\n",
        # Case Management database configuration
        "AM_WF_DATABASE_HOST=": f"AM_WF_DATABASE_HOST={db_host}\n",
        # Knowledge Management database configuration
        "AM_KM_DATABASE_HOST=": f"AM_KM_DATABASE_HOST={db_host}\n",
        # Conversational database configuration
        "AM_CONV_DATABASE_HOST=": f"AM_CONV_DATABASE_HOST={db_host}\n",
        # Single database configuration
        "DATABASE_HOST=": f"DATABASE_HOST={db_host}\n",
    }


def update_env_file(env_type: EnvironmentType) -> None:
    """Update the .env file with the correct values for the specified environment.

    Args:
        env_type: The type of environment to update for.

    """
    print(f"Updating environment file for {env_type} environment...")  # noqa: T201
    # Get the root directory (where .env file is located)
    root_dir = Path(__file__).parent.parent.parent.parent
    env_file = root_dir / ".env"

    if not env_file.exists():
        print(f"Error: {env_file} not found")  # noqa: T201
        return

    # Read the current content
    with open(env_file) as f:
        lines = f.readlines()

    # Get the replacements for the specified environment
    replacements = get_replacements(env_type)

    # Process each line
    new_lines = []
    for line in lines:
        for key, new_value in replacements.items():
            if line.startswith(key):
                line = new_value
                break
        new_lines.append(line)

    # Write back to the file
    with open(env_file, "w") as f:
        f.writelines(new_lines)

    print(f"Environment file updated successfully for {env_type} environment!")  # noqa: T201


def main() -> None:
    """Entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Update environment variables for local or docker environment."
    )
    parser.add_argument(
        "environment",
        choices=["local", "docker"],
        help="The environment to update for (local or docker)",
    )

    args = parser.parse_args()
    update_env_file(args.environment)  # type: ignore


if __name__ == "__main__":
    main()
