"""Alembic migration helpers for the database."""

from alembic import op
from sqlalchemy.engine.reflection import Inspector

# Adapted from https://github.com/elau1004/Alembic-Branches-Example/blob/main/myproject/db/alembic/__init__.py


def exist_table(table_name: str) -> bool:
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    tables = inspector.get_table_names()
    return table_name in tables
