"""Refactored SQLAlchemy models for simplified two-table agent conversation tracking."""

import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import (
    JSON,
    DateTime,
    ForeignKey,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    mapped_column,
    query_expression,
    relationship,
)


class Base(DeclarativeBase):
    """Base class for all SQLAlchemy models."""

    pass


class Conversations(Base):
    """SQLAlchemy model for conversations.

    Simplified table storing metadata optimized for listing and filtering
    conversations in the Agent Manager UI.

    Note: message_count and last_message_at are query expressions that are
    calculated efficiently using JOINs when explicitly loaded in queries.
    """

    __tablename__ = "conversations"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    agent_id: Mapped[str] = mapped_column(String, index=True, nullable=False)
    start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    end_time: Mapped[datetime | None] = mapped_column(DateTime(timezone=True), nullable=True)
    custom_fields: Mapped[dict[str, Any] | None] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
    )

    message_turns: Mapped[list["MessageTurn"]] = relationship(
        back_populates="conversations",
        cascade="all, delete-orphan",
        order_by="MessageTurn.timestamp",
    )

    # Efficient query expressions - loaded on demand with JOINs
    message_count: Mapped[int] = query_expression()
    last_message_at: Mapped[datetime | None] = query_expression()

    def __repr__(self) -> str:
        """Return string representation of Conversations."""
        return f"<Conversations(id='{self.id}', agent_id='{self.agent_id}')>"


class MessageTurn(Base):
    """SQLAlchemy model for message turns with structured JSON message storage.

    Represents each individual message or "turn" within a conversation.
    Uses JSONB to store structured message data including sources, metadata,
    and raw OpenTelemetry information for efficient querying and analysis.

    Messages are ordered by timestamp + role, with timestamps from span end_time.
    """

    __tablename__ = "message_turns"

    # Primary identifiers
    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("conversations.id"), nullable=False, index=True
    )
    trace_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True), nullable=True, index=True
    )
    timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)

    # Message content
    role: Mapped[str] = mapped_column(String, nullable=False)
    content_text: Mapped[str | None] = mapped_column(Text, nullable=True)
    raw_message: Mapped[dict | None] = mapped_column(JSON, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Relationships
    conversations: Mapped["Conversations"] = relationship(back_populates="message_turns")

    def __repr__(self) -> str:
        """Return string representation of MessageTurn."""
        return (
            f"<MessageTurn(id={self.id}, conversation_id='{self.conversation_id}', "
            f"timestamp={self.timestamp})>"
        )


class Span(Base):
    """SQLAlchemy model for storing OpenTelemetry spans.

    This model captures trace data including span
    attributes and events for conversational tracking.
    Used in the OpenTelemetry trace ingestion endpoint.
    """

    __tablename__ = "span"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True)
    trace_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), index=True)
    parent_span_id: Mapped[uuid.UUID | None] = mapped_column(UUID(as_uuid=True), nullable=True)
    name: Mapped[str] = mapped_column(String, nullable=False)
    start_time: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    end_time: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    attributes: Mapped[dict[str, Any]] = mapped_column(JSON, nullable=False)
    events: Mapped[dict[str, Any]] = mapped_column(JSON, nullable=False)

    def __repr__(self) -> str:
        """Return string representation of Span."""
        return f"<Span(id={self.id}, name='{self.name}')>"
