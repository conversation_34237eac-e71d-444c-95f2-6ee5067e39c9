# Workflow Configuration Guide

This guide explains how to create workflow configuration files for the Gen-OS Agent Manager system. Workflow configuration files define the structure, steps, inputs, outputs, and execution flow of automated processes.

## Table of Contents

1. [Overview](#overview)
2. [File Structure](#file-structure)
3. [Workflow Configuration](#workflow-configuration)
4. [Step Configuration](#step-configuration)
5. [Input/Output Configuration](#inputoutput-configuration)
6. [Step Types](#step-types)
7. [Step Block Types](#step-block-types)
8. [Validation Rules](#validation-rules)
9. [Best Practices](#best-practices)
10. [Complete Examples](#complete-examples)

## Overview

Workflow configuration files are YAML documents that define automated business processes. Each workflow consists of a series of steps that execute in sequence, with inputs and outputs flowing between steps.

### Key Concepts

- **Workflow**: A complete business process definition
- **Step**: An individual task or operation within a workflow
- **Input**: Data or files that a step consumes
- **Output**: Data or files that a step produces
- **Step Block**: A typed data container for inputs and outputs

## File Structure

```yaml
workflow:
  name: string                    # Required: Unique workflow identifier
  description: string             # Optional: Human-readable description
  steps:                         # Required: List of workflow steps
    - name: string               # Required: Step identifier
      description: string        # Optional: Step description
      step_type: string          # Required: "gen_ai", "tool", or "action"
      order_number: integer      # Required: Step execution order
      inputs: []                 # Optional: List of step inputs
      outputs: []                # Optional: List of step outputs
```

## Workflow Configuration

### Required Fields

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Unique identifier for the workflow |
| `steps` | array | List of steps to execute |

### Optional Fields

| Field | Type | Description |
|-------|------|-------------|
| `description` | string | Human-readable workflow description |

### Example

```yaml
workflow:
  name: 'invoice-processing'
  description: 'Automated invoice processing and PO matching'
```

## Step Configuration

### Required Fields

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Unique step identifier within the workflow |
| `step_type` | string | Type of step: "gen_ai", "tool", or "action" |
| `order_number` | integer | Execution order (starting from 1) |

### Optional Fields

| Field | Type | Description |
|-------|------|-------------|
| `description` | string | Human-readable step description |
| `inputs` | array | List of input configurations |
| `outputs` | array | List of output configurations |

### Example

```yaml
- name: 'extract-fields'
  description: 'Extract structured data from uploaded document'
  step_type: 'gen_ai'
  order_number: 1
```

## Input/Output Configuration

### Input Configuration

| Field | Type | Description |
|-------|------|-------------|
| `step_block_name` | string | Unique identifier for the input |
| `step_block_type` | string | Type of data block (see Step Block Types) |
| `status` | string | "editable" or "non_editable" |
| `order_number` | integer | Display order |
| `previous_step_fed` | object | Reference to previous step output |
| `type_extra` | array/object | Additional type-specific configuration |

### Output Configuration

| Field | Type | Description |
|-------|------|-------------|
| `step_block_name` | string | Unique identifier for the output |
| `step_block_type` | string | Type of data block (see Step Block Types) |
| `status` | string | "editable" or "non_editable" |
| `order_number` | integer | Display order |
| `type_extra` | array/object | Additional type-specific configuration |

### Previous Step Reference

```yaml
previous_step_fed:
  step_name: 'source-step-name'
  step_block_name: 'source-output-name'
  type_extra: {}
```

## Step Types

### 1. Gen AI (`gen_ai`)

- **Purpose**: AI-powered processing steps
- **Input Constraints**: Only `non_editable` inputs allowed
- **Output Constraints**: Any output status allowed
- **Use Cases**: Text extraction, data transformation, analysis

### 2. Tool (`tool`)

- **Purpose**: External tool integration
- **Input Constraints**: Only `non_editable` inputs allowed
- **Output Constraints**: Any output status allowed
- **Use Cases**: API calls, file processing, data validation

### 3. Action (`action`)

- **Purpose**: System actions and side effects
- **Input Constraints**: Both `editable` and `non_editable` inputs allowed
- **Output Constraints**: No outputs allowed
- **Use Cases**: Database writes, email sending, file uploads

## Step Block Types

### 1. Dataset (`dataset`)

- **Purpose**: Structured tabular data
- **Configuration**: No additional type_extra required

### 2. File (`file`)

- **Purpose**: Handling files (either URL or binary)
- **Configuration**: No additional type_extra required

### 3. Selection (`selection`)

- **Purpose**: User choice from predefined options
- **Configuration**: `type_extra` must contain selection options

### 4. Text Structured (`text_structured`)

- **Purpose**: Structured text with defined fields
- **Configuration**: `type_extra` must define field schema

```yaml
type_extra:
  - field_name: 'Invoice Number'
    field_type: 'string'
  - field_name: 'Amount'
    field_type: 'number'
```

### 5. Text Unstructured (`text_unstructured`)

- **Purpose**: Free-form text content
- **Configuration**: No additional type_extra required

### 6. Tool (`tool`)

- **Purpose**: Tool-specific data structures
- **Configuration**: Tool-dependent type_extra

### 7. Email (`email`)

- **Purpose**: Email message content
- **Configuration**: No additional type_extra required

## Validation Rules

### Workflow Level

- Workflow `name` must be unique
- At least one step must be defined

### Step Level

- Step `name` must be unique within the workflow
- `order_number` must be positive and sequential
- Step type must be valid: "gen_ai", "tool", or "action"

### Input/Output Level

- `step_block_name` must be unique within the step
- `step_block_type` must be valid
- `status` must be "editable" or "non_editable"
- `type_extra` is required for "selection" and "text_structured" types

### Step Type Constraints

- `gen_ai` and `tool` steps: only `non_editable` inputs
- `action` steps: no outputs allowed
- `action` steps: both `editable` and `non_editable` inputs allowed

## Best Practices

### 1. Naming Conventions

- Use descriptive, kebab-case names for workflows and steps
- Use clear, business-relevant names for step blocks
- Include action verbs in step names (e.g., "extract-fields", "validate-data")

### 2. Step Organization

- Keep steps focused on single responsibilities
- Use logical order numbers with gaps (10, 20, 30) for easier reordering
- Group related functionality into separate steps

### 3. Data Flow

- Clearly define input/output relationships
- Use consistent field names across steps
- Document field purposes in descriptions

### 4. Error Handling

- Include validation steps after data extraction
- Use appropriate step types for different operations
- Plan for manual intervention points

### 5. Configuration Management

- Include timestamps for tracking changes
- Add comprehensive descriptions for complex steps
- Use consistent formatting and indentation

## Complete Examples

### Invoice Processing Workflow

```yaml
workflow:
  name: 'invoice-processing'
  description: 'Automated invoice processing with PO matching'
  steps:
    - name: 'extract-invoice-data'
      description: 'Extract structured fields from invoice PDF'
      step_type: 'gen_ai'
      order_number: 1
      inputs:
        - step_block_name: 'invoice_pdf'
          status: 'non_editable'
          step_block_type: 'file'
          order_number: 1
      outputs:
        - step_block_name: 'invoice_fields'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          type_extra:
            - field_name: 'Invoice Number'
              field_type: 'string'
            - field_name: 'Amount'
              field_type: 'number'
            - field_name: 'Supplier Name'
              field_type: 'string'

    - name: 'find-purchase-orders'
      description: 'Identify related purchase orders'
      step_type: 'gen_ai'
      order_number: 2
      inputs:
        - step_block_name: 'invoice_fields'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          previous_step_fed:
            step_name: 'extract-invoice-data'
            step_block_name: 'invoice_fields'
      outputs:
        - step_block_name: 'purchase_orders'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1

    - name: 'update-erp-system'
      description: 'Update ERP system with matched data'
      step_type: 'action'
      order_number: 3
      inputs:
        - step_block_name: 'purchase_orders'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1
          previous_step_fed:
            step_name: 'find-purchase-orders'
            step_block_name: 'purchase_orders'
```

### Quotation Processing Workflow

```yaml
workflow:
  name: 'quotation-processing'
  description: 'Process quotation requests and generate responses'
  steps:
    - name: 'extract-requirements'
      description: 'Extract product requirements from request'
      step_type: 'gen_ai'
      order_number: 1
      inputs:
        - step_block_name: 'request_text'
          status: 'non_editable'
          step_block_type: 'text_unstructured'
          order_number: 1
      outputs:
        - step_block_name: 'product_requirements'
          status: 'editable'
          step_block_type: 'dataset'
          order_number: 1

    - name: 'match-products'
      description: 'Find matching products in catalog'
      step_type: 'tool'
      order_number: 2
      inputs:
        - step_block_name: 'product_requirements'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1
          previous_step_fed:
            step_name: 'extract-requirements'
            step_block_name: 'product_requirements'
      outputs:
        - step_block_name: 'matched_products'
          status: 'editable'
          step_block_type: 'dataset'
          order_number: 1

    - name: 'generate-quote'
      description: 'Generate quotation document'
      step_type: 'gen_ai'
      order_number: 3
      inputs:
        - step_block_name: 'matched_products'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1
          previous_step_fed:
            step_name: 'match-products'
            step_block_name: 'matched_products'
      outputs:
        - step_block_name: 'quotation_document'
          status: 'editable'
          step_block_type: 'text_structured'
          order_number: 1
          type_extra:
            - field_name: 'Quote Number'
              field_type: 'string'
            - field_name: 'Total Amount'
              field_type: 'number'
            - field_name: 'Validity Period'
              field_type: 'string'

    - name: 'send-quotation'
      description: 'Send quotation to customer'
      step_type: 'action'
      order_number: 4
      inputs:
        - step_block_name: 'quotation_document'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          previous_step_fed:
            step_name: 'generate-quote'
            step_block_name: 'quotation_document'
        - step_block_name: 'customer_email'
          status: 'editable'
          step_block_type: 'email'
          order_number: 2
```

This documentation provides a comprehensive guide for creating workflow configuration files that are compatible with the Gen-OS Agent Manager database schema and execution engine.
