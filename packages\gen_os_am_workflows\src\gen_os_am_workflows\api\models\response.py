"""Response models for the API."""

import uuid
from datetime import datetime
from typing import Any
from uuid import UUID

from pydantic import BaseModel, Field


class MessageResponse(BaseModel):
    """Response model for message."""

    message: str


class FileUploadResponse(BaseModel):
    """Response model for file upload."""

    file_id: UUID
    upload_url: str


class FileDownloadResponse(BaseModel):
    """Response model for file download."""

    id: UUID
    url: str
    file_name: str


class EmailExistsResponse(BaseModel):
    """Response model for email exists check."""

    exists: bool


class EmailMessageIdsResponse(BaseModel):
    """Response model for email message IDs."""

    message_ids: list[str]


class WorkflowExecutionWithIssuesResponse(BaseModel):
    """Response model for workflow execution with reported issues."""

    # WorkflowExecution fields
    id: uuid.UUID
    status: str
    created_at: datetime
    updated_at: datetime
    test_execution: bool | None = None

    # Workflow data (nested)
    workflow: dict[str, Any] = Field(default_factory=dict)

    # Issues integration
    reported_issues: list[dict[str, Any]] = Field(
        default_factory=list,
        description="List of issues reported for this workflow execution"
    )
