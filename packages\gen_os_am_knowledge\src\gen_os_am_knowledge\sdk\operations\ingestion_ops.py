"""Module for ingestion operations."""

import logging
import uuid
from datetime import datetime, timezone
from typing import Any

from gen_os_sdk_emulator.core.database.session import SessionManager
from sqlmodel import delete, select

from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.config.settings import DatabaseSettings
from gen_os_am_knowledge.sdk.models import Ingestion
from gen_os_am_knowledge.sdk.operations.utils import (
    NOT_PROVIDED,
    DeletionResult,
    UpdateResult,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
    start_time_var,
)

db_settings = DatabaseSettings.get_settings()


class IngestionService:
    """Service class to handle CRUD operations for ingestion model."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the IngestionService with an optional logger."""
        self.logger = logger or get_sdk_logger()

    @log_decorator()
    async def create_ingestion(
        self,
        source_id: uuid.UUID,
        description: str,
        folder_id: uuid.UUID,
        meta: dict[str, Any] | None = None,
    ) -> Ingestion:
        """Create a new ingestion.

        Args:
            source_id: The id of the source.
            description: The description of the ingestion.
            folder_id: The id of the root folder for ingestion.
            meta: The metadata of the ingestion.

        Returns:
            Ingestion: The created ingestion instance.

        """
        async with SessionManager.get_session(db_settings) as session:
            ingestion = Ingestion(
                source_id=source_id,
                description=description,
                meta=meta,
                folder_id=folder_id,
            )

            session.add(ingestion)
            await session.commit()
            await session.refresh(ingestion)
            return ingestion

    @log_decorator()
    async def delete_ingestion(self, ingestion_id: uuid.UUID) -> DeletionResult:
        """Delete ingestion and return the result of the operation.

        Args:
            ingestion_id: The id of the ingestion to delete.

        Returns:
            DeletionResult: The result of the deletion operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = delete(Ingestion).where(Ingestion.id == ingestion_id)
            deleted_ingestion = (
                await session.execute(query.returning(Ingestion))
            ).scalar_one_or_none()
            if not deleted_ingestion:
                error_event_log(
                    msg=f"Ingestion with id {ingestion_id} not found.",
                    start=start,
                    error=Exception(f"Ingestion with id {ingestion_id} not found."),
                    logger=self.logger,
                )
                return DeletionResult(
                    success=False, error=f"Ingestion with id {ingestion_id} not found."
                )
            else:
                deleted_ingestion = deleted_ingestion.model_copy()
                await session.commit()
                return DeletionResult(success=True, deleted_instance=deleted_ingestion)

    @log_decorator(type_="access")
    async def get_ingestion(self, ingestion_id: uuid.UUID) -> Ingestion | None:
        """Retrieve ingestion from the database based on ingestion id.

        Args:
            ingestion_id: The ingestion id to search for.

        Returns:
            Ingestion: The ingestion instance if found, None otherwise.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Ingestion).where(Ingestion.id == ingestion_id)
            return (await session.execute(query)).scalars().first()

    @log_decorator(type_="access")
    async def search_ingestions(
        self,
        description: str | None = None,
        status: Ingestion._ING_STATUS_TYPE | None = None,
        source_id: uuid.UUID | None = None,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Ingestion]:
        """Retrieve ingestions from the database.

        If None of the attributes is given, retrieves all the ingestions.

        Args:
            description: The description of the ingestion to search for.
            status: The status of the ingestion to search for.
            source_id: The source id of the ingestion to search for.
            limit: The limit of the number of ingestions to retrieve.
            offset: The offset of the number of ingestions to retrieve

        Returns:
            list[Ingestion]: The list of ingestions that match the search criteria.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Ingestion)
            if description:
                query = query.where(Ingestion.description == description)
            if status:
                query = query.where(Ingestion.status == status)
            if source_id:
                query = query.where(Ingestion.source_id == source_id)

            query = query.limit(limit).offset(offset)
            return (await session.execute(query)).scalars().all()

    @log_decorator()
    async def update_ingestion(
        self,
        ingestion_id: uuid.UUID,
        description: str | type[NOT_PROVIDED] = NOT_PROVIDED,
        status: Ingestion._ING_STATUS_TYPE | type[NOT_PROVIDED] = NOT_PROVIDED,
        source_id: uuid.UUID | type[NOT_PROVIDED] = NOT_PROVIDED,
        last_run_date: datetime | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        last_success_date: datetime | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        meta: dict[str, Any] | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        error: str | None | type[NOT_PROVIDED] = NOT_PROVIDED,
    ) -> UpdateResult:
        """Update an ingestion in the database.

        Note! For some fields it is possible to pass None to update the field to None.

        Args:
            ingestion_id: The id of the ingestion to update. Only required argument.
            description: str  - The new description of the ingestion.
            status: Ingestion._ING_STATUS_TYPE  - The new status of the ingestion.
            source_id: uuid.UUID  - The new source id of the ingestion.
            last_run_date: datetime | None  - The new last run date of the ingestion.
            last_success_date: datetime | None  -
                The new last success date of the ingestion.
            meta: dict[str, Any] | None  - The new meta of the ingestion.
            error: str | None - The new error of the ingestion.

        Returns:
            UpdateResult: The result of the update operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        if isinstance(last_run_date, str):
            last_run_date = datetime.fromisoformat(last_run_date)
        if isinstance(last_success_date, str):
            last_success_date = datetime.fromisoformat(last_success_date)
        async with SessionManager.get_session(db_settings) as session:
            query = select(Ingestion).where(Ingestion.id == ingestion_id)
            ingestion = (await session.execute(query)).scalar_one_or_none()
            if not ingestion:
                error_event_log(
                    msg=f"Ingestion with id {ingestion_id} not found.",
                    start=start,
                    error=Exception(f"Ingestion with id {ingestion_id} not found."),
                    logger=self.logger,
                )
                return UpdateResult(
                    success=False, error=f"Ingestion with id {ingestion_id} not found."
                )
            else:
                update_dict = {
                    "description": description,
                    "status": status,
                    "source_id": source_id,
                    "last_run_date": last_run_date,
                    "last_success_date": last_success_date,
                    "meta": meta,
                    "error": error,
                }
                updated_values = {}
                for k, v in update_dict.items():
                    if v is not NOT_PROVIDED:
                        if getattr(ingestion, k) != v:
                            updated_values[k] = (getattr(ingestion, k), v)
                            setattr(ingestion, k, v)
                session.add(ingestion)
                ingestion = ingestion.model_copy()
                await session.commit()
                return UpdateResult(
                    success=True,
                    updated_instance=ingestion,
                    updated_values=updated_values,
                )
