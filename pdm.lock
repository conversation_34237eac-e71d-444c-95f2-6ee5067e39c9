# This file is @generated by PDM.
# It is not intended for manual editing.

[metadata]
groups = ["default", "dev", "doc", "notebooks", "test"]
strategy = ["inherit_metadata"]
lock_version = "4.5.0"
content_hash = "sha256:5087e791ec3d67cf21813b6b802f2a744a32064df53a35dfdc14b000946c8f0c"

[[metadata.targets]]
requires_python = "==3.12.3"
platform = "manylinux_2_39_x86_64"
implementation = "cpython"
gil_disabled = false

[[package]]
name = "babel"
version = "2.17.0"
requires_python = ">=3.8"
summary = "Internationalization utilities"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "pytz>=2015.7; python_version < \"3.9\"",
]
files = [
    {file = "babel-2.17.0-py3-none-any.whl", hash = "sha256:4d0b53093fdfb4b21c92b5213dba5a1b23885afa8383709427046b21c366e5f2"},
    {file = "babel-2.17.0.tar.gz", hash = "sha256:0c54cffb19f690cdcc52a3b50bcbf71e07a808d1c80d549f2459b9d2cf0afb9d"},
]

[[package]]
name = "backrefs"
version = "5.9"
requires_python = ">=3.9"
summary = "A wrapper around re and regex that adds additional back references."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "backrefs-5.9-py312-none-any.whl", hash = "sha256:7fdf9771f63e6028d7fee7e0c497c81abda597ea45d6b8f89e8ad76994f5befa"},
    {file = "backrefs-5.9.tar.gz", hash = "sha256:808548cb708d66b82ee231f962cb36faaf4f2baab032f2fbb783e9c2fdddaa59"},
]

[[package]]
name = "bracex"
version = "2.6"
requires_python = ">=3.9"
summary = "Bash style brace expander."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "bracex-2.6-py3-none-any.whl", hash = "sha256:0b0049264e7340b3ec782b5cb99beb325f36c3782a32e36e876452fd49a09952"},
    {file = "bracex-2.6.tar.gz", hash = "sha256:98f1347cd77e22ee8d967a30ad4e310b233f7754dbf31ff3fceb76145ba47dc7"},
]

[[package]]
name = "build"
version = "1.2.2.post1"
requires_python = ">=3.8"
summary = "A simple, correct Python build frontend"
groups = ["default"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "colorama; os_name == \"nt\"",
    "importlib-metadata>=4.6; python_full_version < \"3.10.2\"",
    "packaging>=19.1",
    "pyproject-hooks",
    "tomli>=1.1.0; python_version < \"3.11\"",
]
files = [
    {file = "build-1.2.2.post1-py3-none-any.whl", hash = "sha256:1d61c0887fa860c01971625baae8bdd338e517b836a2f70dd1f7aa3a6b2fc5b5"},
    {file = "build-1.2.2.post1.tar.gz", hash = "sha256:b36993e92ca9375a219c99e606a122ff365a760a2d4bba0caa09bd5278b608b7"},
]

[[package]]
name = "certifi"
version = "2025.7.14"
requires_python = ">=3.7"
summary = "Python package for providing Mozilla's CA Bundle."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "certifi-2025.7.14-py3-none-any.whl", hash = "sha256:6b31f564a415d79ee77df69d757bb49a5bb53bd9f756cbbe24394ffd6fc1f4b2"},
    {file = "certifi-2025.7.14.tar.gz", hash = "sha256:8ea99dbdfaaf2ba2f9bac77b9249ef62ec5218e7c2b2e903378ed5fccf765995"},
]

[[package]]
name = "cfgv"
version = "3.4.0"
requires_python = ">=3.8"
summary = "Validate configuration and produce human readable error messages."
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "cfgv-3.4.0-py2.py3-none-any.whl", hash = "sha256:b7265b1f29fd3316bfcd2b330d63d024f2bfd8bcb8b0272f8e19a504856c48f9"},
    {file = "cfgv-3.4.0.tar.gz", hash = "sha256:e52591d4c5f5dead8e0f673fb16db7949d2cfb3f7da4582893288f0ded8fe560"},
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
requires_python = ">=3.7"
summary = "The Real First Universal Charset Detector. Open, modern and actively maintained alternative to Chardet."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "charset_normalizer-3.4.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:4e594135de17ab3866138f496755f302b72157d115086d100c3f19370839dd3a"},
    {file = "charset_normalizer-3.4.2-py3-none-any.whl", hash = "sha256:7f56930ab0abd1c45cd15be65cc741c28b1c9a34876ce8c17a2fa107810c0af0"},
    {file = "charset_normalizer-3.4.2.tar.gz", hash = "sha256:5baececa9ecba31eff645232d59845c07aa030f0c81ee70184a90d35099a0e63"},
]

[[package]]
name = "click"
version = "8.2.1"
requires_python = ">=3.10"
summary = "Composable command line interface toolkit"
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "colorama; platform_system == \"Windows\"",
]
files = [
    {file = "click-8.2.1-py3-none-any.whl", hash = "sha256:61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b"},
    {file = "click-8.2.1.tar.gz", hash = "sha256:27c491cc05d968d271d5a1db13e3b5a184636d9d930f148c50b038f0d0646202"},
]

[[package]]
name = "colorama"
version = "0.4.6"
requires_python = "!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*,>=2.7"
summary = "Cross-platform colored terminal text."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6"},
    {file = "colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44"},
]

[[package]]
name = "distlib"
version = "0.3.9"
summary = "Distribution utilities"
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "distlib-0.3.9-py2.py3-none-any.whl", hash = "sha256:47f8c22fd27c27e25a65601af709b38e4f0a45ea4fc2e710f65755fa8caaaf87"},
    {file = "distlib-0.3.9.tar.gz", hash = "sha256:a60f20dea646b8a33f3e7772f74dc0b2d0772d2837ee1342a00645c81edf9403"},
]

[[package]]
name = "filelock"
version = "3.18.0"
requires_python = ">=3.9"
summary = "A platform independent file lock."
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "filelock-3.18.0-py3-none-any.whl", hash = "sha256:c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de"},
    {file = "filelock-3.18.0.tar.gz", hash = "sha256:adbc88eabb99d2fec8c9c1b229b171f18afa655400173ddc653d5d01501fb9f2"},
]

[[package]]
name = "ghp-import"
version = "2.1.0"
summary = "Copy your docs directly to the gh-pages branch."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "python-dateutil>=2.8.1",
]
files = [
    {file = "ghp-import-2.1.0.tar.gz", hash = "sha256:9c535c4c61193c2df8871222567d7fd7e5014d835f97dc7b7439069e2413d343"},
    {file = "ghp_import-2.1.0-py3-none-any.whl", hash = "sha256:8337dd7b50877f163d4c0289bc1f1c7f127550241988d568c1db512c4324a619"},
]

[[package]]
name = "griffe"
version = "1.7.3"
requires_python = ">=3.9"
summary = "Signatures for entire Python programs. Extract the structure, the frame, the skeleton of your project, to generate API documentation or find breaking changes in your API."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "colorama>=0.4",
]
files = [
    {file = "griffe-1.7.3-py3-none-any.whl", hash = "sha256:c6b3ee30c2f0f17f30bcdef5068d6ab7a2a4f1b8bf1a3e74b56fffd21e1c5f75"},
    {file = "griffe-1.7.3.tar.gz", hash = "sha256:52ee893c6a3a968b639ace8015bec9d36594961e156e23315c8e8e51401fa50b"},
]

[[package]]
name = "hjson"
version = "3.1.0"
summary = "Hjson, a user interface for JSON."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "hjson-3.1.0-py3-none-any.whl", hash = "sha256:65713cdcf13214fb554eb8b4ef803419733f4f5e551047c9b711098ab7186b89"},
    {file = "hjson-3.1.0.tar.gz", hash = "sha256:55af475a27cf83a7969c808399d7bccdec8fb836a07ddbd574587593b9cdcf75"},
]

[[package]]
name = "identify"
version = "2.6.12"
requires_python = ">=3.9"
summary = "File identification library for Python"
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "identify-2.6.12-py2.py3-none-any.whl", hash = "sha256:ad9672d5a72e0d2ff7c5c8809b62dfa60458626352fb0eb7b55e69bdc45334a2"},
    {file = "identify-2.6.12.tar.gz", hash = "sha256:d8de45749f1efb108badef65ee8386f0f7bb19a7f26185f74de6367bffbaf0e6"},
]

[[package]]
name = "idna"
version = "3.10"
requires_python = ">=3.6"
summary = "Internationalized Domain Names in Applications (IDNA)"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "idna-3.10-py3-none-any.whl", hash = "sha256:946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3"},
    {file = "idna-3.10.tar.gz", hash = "sha256:12f65c9b470abda6dc35cf8e63cc574b1c52b11df2c86030af0ac09b01b13ea9"},
]

[[package]]
name = "jinja2"
version = "3.1.6"
requires_python = ">=3.7"
summary = "A very fast and expressive template engine."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "MarkupSafe>=2.0",
]
files = [
    {file = "jinja2-3.1.6-py3-none-any.whl", hash = "sha256:85ece4451f492d0c13c5dd7c13a64681a86afae63a5f347908daf103ce6d2f67"},
    {file = "jinja2-3.1.6.tar.gz", hash = "sha256:0137fb05990d35f1275a587e9aee6d56da821fc83491a0fb838183be43f66d6d"},
]

[[package]]
name = "markdown"
version = "3.8.2"
requires_python = ">=3.9"
summary = "Python implementation of John Gruber's Markdown."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "importlib-metadata>=4.4; python_version < \"3.10\"",
]
files = [
    {file = "markdown-3.8.2-py3-none-any.whl", hash = "sha256:5c83764dbd4e00bdd94d85a19b8d55ccca20fe35b2e678a1422b380324dd5f24"},
    {file = "markdown-3.8.2.tar.gz", hash = "sha256:247b9a70dd12e27f67431ce62523e675b866d254f900c4fe75ce3dda62237c45"},
]

[[package]]
name = "markupsafe"
version = "3.0.2"
requires_python = ">=3.9"
summary = "Safely add untrusted strings to HTML/XML markup."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "MarkupSafe-3.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:e17c96c14e19278594aa4841ec148115f9c7615a47382ecb6b82bd8fea3ab0c8"},
    {file = "markupsafe-3.0.2.tar.gz", hash = "sha256:ee55d3edf80167e48ea11a923c7386f4669df67d7994554387f84e7d8b0a2bf0"},
]

[[package]]
name = "mergedeep"
version = "1.3.4"
requires_python = ">=3.6"
summary = "A deep merge function for 🐍."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "mergedeep-1.3.4-py3-none-any.whl", hash = "sha256:70775750742b25c0d8f36c55aed03d24c3384d17c951b3175d898bd778ef0307"},
    {file = "mergedeep-1.3.4.tar.gz", hash = "sha256:0096d52e9dad9939c3d975a774666af186eda617e6ca84df4c94dec30004f2a8"},
]

[[package]]
name = "mkdocs"
version = "1.6.1"
requires_python = ">=3.8"
summary = "Project documentation with Markdown."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "click>=7.0",
    "colorama>=0.4; platform_system == \"Windows\"",
    "ghp-import>=1.0",
    "importlib-metadata>=4.4; python_version < \"3.10\"",
    "jinja2>=2.11.1",
    "markdown>=3.3.6",
    "markupsafe>=2.0.1",
    "mergedeep>=1.3.4",
    "mkdocs-get-deps>=0.2.0",
    "packaging>=20.5",
    "pathspec>=0.11.1",
    "pyyaml-env-tag>=0.1",
    "pyyaml>=5.1",
    "watchdog>=2.0",
]
files = [
    {file = "mkdocs-1.6.1-py3-none-any.whl", hash = "sha256:db91759624d1647f3f34aa0c3f327dd2601beae39a366d6e064c03468d35c20e"},
    {file = "mkdocs-1.6.1.tar.gz", hash = "sha256:7b432f01d928c084353ab39c57282f29f92136665bdd6abf7c1ec8d822ef86f2"},
]

[[package]]
name = "mkdocs-autorefs"
version = "1.4.2"
requires_python = ">=3.9"
summary = "Automatically link across pages in MkDocs."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "Markdown>=3.3",
    "markupsafe>=2.0.1",
    "mkdocs>=1.1",
]
files = [
    {file = "mkdocs_autorefs-1.4.2-py3-none-any.whl", hash = "sha256:83d6d777b66ec3c372a1aad4ae0cf77c243ba5bcda5bf0c6b8a2c5e7a3d89f13"},
    {file = "mkdocs_autorefs-1.4.2.tar.gz", hash = "sha256:e2ebe1abd2b67d597ed19378c0fff84d73d1dbce411fce7a7cc6f161888b6749"},
]

[[package]]
name = "mkdocs-get-deps"
version = "0.2.0"
requires_python = ">=3.8"
summary = "MkDocs extension that lists all dependencies according to a mkdocs.yml file"
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "importlib-metadata>=4.3; python_version < \"3.10\"",
    "mergedeep>=1.3.4",
    "platformdirs>=2.2.0",
    "pyyaml>=5.1",
]
files = [
    {file = "mkdocs_get_deps-0.2.0-py3-none-any.whl", hash = "sha256:2bf11d0b133e77a0dd036abeeb06dec8775e46efa526dc70667d8863eefc6134"},
    {file = "mkdocs_get_deps-0.2.0.tar.gz", hash = "sha256:162b3d129c7fad9b19abfdcb9c1458a651628e4b1dea628ac68790fb3061c60c"},
]

[[package]]
name = "mkdocs-include-markdown-plugin"
version = "7.1.6"
requires_python = ">=3.9"
summary = "Mkdocs Markdown includer plugin."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "mkdocs>=1.4",
    "wcmatch",
]
files = [
    {file = "mkdocs_include_markdown_plugin-7.1.6-py3-none-any.whl", hash = "sha256:7975a593514887c18ecb68e11e35c074c5499cfa3e51b18cd16323862e1f7345"},
    {file = "mkdocs_include_markdown_plugin-7.1.6.tar.gz", hash = "sha256:a0753cb82704c10a287f1e789fc9848f82b6beb8749814b24b03dd9f67816677"},
]

[[package]]
name = "mkdocs-macros-plugin"
version = "1.3.7"
requires_python = ">=3.8"
summary = "Unleash the power of MkDocs with macros and variables"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "hjson",
    "jinja2",
    "mkdocs>=0.17",
    "packaging",
    "pathspec",
    "python-dateutil",
    "pyyaml",
    "super-collections",
    "termcolor",
]
files = [
    {file = "mkdocs_macros_plugin-1.3.7-py3-none-any.whl", hash = "sha256:02432033a5b77fb247d6ec7924e72fc4ceec264165b1644ab8d0dc159c22ce59"},
    {file = "mkdocs_macros_plugin-1.3.7.tar.gz", hash = "sha256:17c7fd1a49b94defcdb502fd453d17a1e730f8836523379d21292eb2be4cb523"},
]

[[package]]
name = "mkdocs-material"
version = "9.6.15"
requires_python = ">=3.8"
summary = "Documentation that simply works"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "babel~=2.10",
    "backrefs~=5.7.post1",
    "colorama~=0.4",
    "jinja2~=3.1",
    "markdown~=3.2",
    "mkdocs-material-extensions~=1.3",
    "mkdocs~=1.6",
    "paginate~=0.5",
    "pygments~=2.16",
    "pymdown-extensions~=10.2",
    "requests~=2.26",
]
files = [
    {file = "mkdocs_material-9.6.15-py3-none-any.whl", hash = "sha256:ac969c94d4fe5eb7c924b6d2f43d7db41159ea91553d18a9afc4780c34f2717a"},
    {file = "mkdocs_material-9.6.15.tar.gz", hash = "sha256:64adf8fa8dba1a17905b6aee1894a5aafd966d4aeb44a11088519b0f5ca4f1b5"},
]

[[package]]
name = "mkdocs-material-extensions"
version = "1.3.1"
requires_python = ">=3.8"
summary = "Extension pack for Python Markdown and MkDocs Material."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "mkdocs_material_extensions-1.3.1-py3-none-any.whl", hash = "sha256:adff8b62700b25cb77b53358dad940f3ef973dd6db797907c49e3c2ef3ab4e31"},
    {file = "mkdocs_material_extensions-1.3.1.tar.gz", hash = "sha256:10c9511cea88f568257f960358a467d12b970e1f7b2c0e5fb2bb48cab1928443"},
]

[[package]]
name = "mkdocstrings"
version = "0.29.1"
requires_python = ">=3.9"
summary = "Automatic documentation from sources, for MkDocs."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "Jinja2>=2.11.1",
    "Markdown>=3.6",
    "MarkupSafe>=1.1",
    "importlib-metadata>=4.6; python_version < \"3.10\"",
    "mkdocs-autorefs>=1.4",
    "mkdocs>=1.6",
    "pymdown-extensions>=6.3",
]
files = [
    {file = "mkdocstrings-0.29.1-py3-none-any.whl", hash = "sha256:37a9736134934eea89cbd055a513d40a020d87dfcae9e3052c2a6b8cd4af09b6"},
    {file = "mkdocstrings-0.29.1.tar.gz", hash = "sha256:8722f8f8c5cd75da56671e0a0c1bbed1df9946c0cef74794d6141b34011abd42"},
]

[[package]]
name = "mkdocstrings-python"
version = "1.16.12"
requires_python = ">=3.9"
summary = "A Python handler for mkdocstrings."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "griffe>=1.6.2",
    "mkdocs-autorefs>=1.4",
    "mkdocstrings>=0.28.3",
    "typing-extensions>=4.0; python_version < \"3.11\"",
]
files = [
    {file = "mkdocstrings_python-1.16.12-py3-none-any.whl", hash = "sha256:22ded3a63b3d823d57457a70ff9860d5a4de9e8b1e482876fc9baabaf6f5f374"},
    {file = "mkdocstrings_python-1.16.12.tar.gz", hash = "sha256:9b9eaa066e0024342d433e332a41095c4e429937024945fea511afe58f63175d"},
]

[[package]]
name = "nodeenv"
version = "1.9.1"
requires_python = "!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*,>=2.7"
summary = "Node.js virtual environment builder"
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "nodeenv-1.9.1-py2.py3-none-any.whl", hash = "sha256:ba11c9782d29c27c70ffbdda2d7415098754709be8a7056d79a737cd901155c9"},
    {file = "nodeenv-1.9.1.tar.gz", hash = "sha256:6ec12890a2dab7946721edbfbcd91f3319c6ccc9aec47be7c7e6b7011ee6645f"},
]

[[package]]
name = "packaging"
version = "25.0"
requires_python = ">=3.8"
summary = "Core utilities for Python packages"
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484"},
    {file = "packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f"},
]

[[package]]
name = "paginate"
version = "0.5.7"
summary = "Divides large result sets into pages for easier browsing"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "paginate-0.5.7-py2.py3-none-any.whl", hash = "sha256:b885e2af73abcf01d9559fd5216b57ef722f8c42affbb63942377668e35c7591"},
    {file = "paginate-0.5.7.tar.gz", hash = "sha256:22bd083ab41e1a8b4f3690544afb2c60c25e5c9a63a30fa2f483f6c60c8e5945"},
]

[[package]]
name = "pathspec"
version = "0.12.1"
requires_python = ">=3.8"
summary = "Utility library for gitignore style pattern matching of file paths."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "pathspec-0.12.1-py3-none-any.whl", hash = "sha256:a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08"},
    {file = "pathspec-0.12.1.tar.gz", hash = "sha256:a482d51503a1ab33b1c67a6c3813a26953dbdc71c31dacaef9a838c4e29f5712"},
]

[[package]]
name = "platformdirs"
version = "4.3.8"
requires_python = ">=3.9"
summary = "A small Python package for determining appropriate platform-specific dirs, e.g. a `user data dir`."
groups = ["default", "dev", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "platformdirs-4.3.8-py3-none-any.whl", hash = "sha256:ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4"},
    {file = "platformdirs-4.3.8.tar.gz", hash = "sha256:3d512d96e16bcb959a814c9f348431070822a6496326a4be0911c40b5a74c2bc"},
]

[[package]]
name = "pre-commit"
version = "4.2.0"
requires_python = ">=3.9"
summary = "A framework for managing and maintaining multi-language pre-commit hooks."
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "cfgv>=2.0.0",
    "identify>=1.0.0",
    "nodeenv>=0.11.1",
    "pyyaml>=5.1",
    "virtualenv>=20.10.0",
]
files = [
    {file = "pre_commit-4.2.0-py2.py3-none-any.whl", hash = "sha256:a009ca7205f1eb497d10b845e52c838a98b6cdd2102a6c8e4540e94ee75c58bd"},
    {file = "pre_commit-4.2.0.tar.gz", hash = "sha256:601283b9757afd87d40c4c4a9b2b5de9637a8ea02eaff7adc2d0fb4e04841146"},
]

[[package]]
name = "pygments"
version = "2.19.2"
requires_python = ">=3.8"
summary = "Pygments is a syntax highlighting package written in Python."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "pygments-2.19.2-py3-none-any.whl", hash = "sha256:86540386c03d588bb81d44bc3928634ff26449851e99741617ecb9037ee5ec0b"},
    {file = "pygments-2.19.2.tar.gz", hash = "sha256:636cb2477cec7f8952536970bc533bc43743542f70392ae026374600add5b887"},
]

[[package]]
name = "pymdown-extensions"
version = "10.16"
requires_python = ">=3.9"
summary = "Extension pack for Python Markdown."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "markdown>=3.6",
    "pyyaml",
]
files = [
    {file = "pymdown_extensions-10.16-py3-none-any.whl", hash = "sha256:f5dd064a4db588cb2d95229fc4ee63a1b16cc8b4d0e6145c0899ed8723da1df2"},
    {file = "pymdown_extensions-10.16.tar.gz", hash = "sha256:71dac4fca63fabeffd3eb9038b756161a33ec6e8d230853d3cecf562155ab3de"},
]

[[package]]
name = "pyproject-hooks"
version = "1.2.0"
requires_python = ">=3.7"
summary = "Wrappers to call pyproject.toml-based build backend hooks."
groups = ["default"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "pyproject_hooks-1.2.0-py3-none-any.whl", hash = "sha256:9e5c6bfa8dcc30091c74b0cf803c81fdd29d94f01992a7707bc97babb1141913"},
    {file = "pyproject_hooks-1.2.0.tar.gz", hash = "sha256:1e859bd5c40fae9448642dd871adf459e5e2084186e8d2c2a79a824c970da1f8"},
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
requires_python = "!=3.0.*,!=3.1.*,!=3.2.*,>=2.7"
summary = "Extensions to the standard Python datetime module"
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "six>=1.5",
]
files = [
    {file = "python-dateutil-2.9.0.post0.tar.gz", hash = "sha256:37dd54208da7e1cd875388217d5e00ebd4179249f90fb72437e91a35459a0ad3"},
    {file = "python_dateutil-2.9.0.post0-py2.py3-none-any.whl", hash = "sha256:a8b2bc7bffae282281c8140a97d3aa9c14da0b136dfe83f850eea9a5f7470427"},
]

[[package]]
name = "pyyaml"
version = "6.0.2"
requires_python = ">=3.8"
summary = "YAML parser and emitter for Python"
groups = ["default", "dev", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476"},
    {file = "pyyaml-6.0.2.tar.gz", hash = "sha256:d584d9ec91ad65861cc08d42e834324ef890a082e591037abe114850ff7bbc3e"},
]

[[package]]
name = "pyyaml-env-tag"
version = "1.1"
requires_python = ">=3.9"
summary = "A custom YAML tag for referencing environment variables in YAML files."
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "pyyaml",
]
files = [
    {file = "pyyaml_env_tag-1.1-py3-none-any.whl", hash = "sha256:17109e1a528561e32f026364712fee1264bc2ea6715120891174ed1b980d2e04"},
    {file = "pyyaml_env_tag-1.1.tar.gz", hash = "sha256:2eb38b75a2d21ee0475d6d97ec19c63287a7e140231e4214969d0eac923cd7ff"},
]

[[package]]
name = "requests"
version = "2.32.4"
requires_python = ">=3.8"
summary = "Python HTTP for Humans."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "certifi>=2017.4.17",
    "charset-normalizer<4,>=2",
    "idna<4,>=2.5",
    "urllib3<3,>=1.21.1",
]
files = [
    {file = "requests-2.32.4-py3-none-any.whl", hash = "sha256:27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c"},
    {file = "requests-2.32.4.tar.gz", hash = "sha256:27d0316682c8a29834d3264820024b62a36942083d52caf2f14c0591336d3422"},
]

[[package]]
name = "six"
version = "1.17.0"
requires_python = "!=3.0.*,!=3.1.*,!=3.2.*,>=2.7"
summary = "Python 2 and 3 compatibility utilities"
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "six-1.17.0-py2.py3-none-any.whl", hash = "sha256:4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274"},
    {file = "six-1.17.0.tar.gz", hash = "sha256:ff70335d468e7eb6ec65b95b99d3a2836546063f63acc5171de367e834932a81"},
]

[[package]]
name = "super-collections"
version = "0.5.3"
requires_python = ">=3.8"
summary = "file: README.md"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "hjson",
]
files = [
    {file = "super_collections-0.5.3-py3-none-any.whl", hash = "sha256:907d35b25dc4070910e8254bf2f5c928348af1cf8a1f1e8259e06c666e902cff"},
    {file = "super_collections-0.5.3.tar.gz", hash = "sha256:94c1ec96c0a0d5e8e7d389ed8cde6882ac246940507c5e6b86e91945c2968d46"},
]

[[package]]
name = "termcolor"
version = "3.1.0"
requires_python = ">=3.9"
summary = "ANSI color formatting for output in terminal"
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "termcolor-3.1.0-py3-none-any.whl", hash = "sha256:591dd26b5c2ce03b9e43f391264626557873ce1d379019786f99b0c2bee140aa"},
    {file = "termcolor-3.1.0.tar.gz", hash = "sha256:6a6dd7fbee581909eeec6a756cff1d7f7c376063b14e4a298dc4980309e55970"},
]

[[package]]
name = "urllib3"
version = "2.5.0"
requires_python = ">=3.9"
summary = "HTTP library with thread-safe connection pooling, file post, and more."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "urllib3-2.5.0-py3-none-any.whl", hash = "sha256:e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc"},
    {file = "urllib3-2.5.0.tar.gz", hash = "sha256:3fc47733c7e419d4bc3f6b3dc2b4f890bb743906a30d56ba4a5bfa4bbff92760"},
]

[[package]]
name = "virtualenv"
version = "20.31.2"
requires_python = ">=3.8"
summary = "Virtual Python Environment builder"
groups = ["dev"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "distlib<1,>=0.3.7",
    "filelock<4,>=3.12.2",
    "importlib-metadata>=6.6; python_version < \"3.8\"",
    "platformdirs<5,>=3.9.1",
]
files = [
    {file = "virtualenv-20.31.2-py3-none-any.whl", hash = "sha256:36efd0d9650ee985f0cad72065001e66d49a6f24eb44d98980f630686243cf11"},
    {file = "virtualenv-20.31.2.tar.gz", hash = "sha256:e10c0a9d02835e592521be48b332b6caee6887f332c111aa79a09b9e79efc2af"},
]

[[package]]
name = "watchdog"
version = "6.0.0"
requires_python = ">=3.9"
summary = "Filesystem events monitoring"
groups = ["default", "doc"]
marker = "python_full_version == \"3.12.3\""
files = [
    {file = "watchdog-6.0.0-py3-none-manylinux2014_x86_64.whl", hash = "sha256:20ffe5b202af80ab4266dcd3e91aae72bf2da48c0d33bdb15c66658e685e94e2"},
    {file = "watchdog-6.0.0.tar.gz", hash = "sha256:9ddf7c82fda3ae8e24decda1338ede66e1c99883db93711d8fb941eaa2d8c282"},
]

[[package]]
name = "wcmatch"
version = "10.1"
requires_python = ">=3.9"
summary = "Wildcard/glob file name matcher."
groups = ["doc"]
marker = "python_full_version == \"3.12.3\""
dependencies = [
    "bracex>=2.1.1",
]
files = [
    {file = "wcmatch-10.1-py3-none-any.whl", hash = "sha256:5848ace7dbb0476e5e55ab63c6bbd529745089343427caa5537f230cc01beb8a"},
    {file = "wcmatch-10.1.tar.gz", hash = "sha256:f11f94208c8c8484a16f4f48638a85d771d9513f4ab3f37595978801cb9465af"},
]
