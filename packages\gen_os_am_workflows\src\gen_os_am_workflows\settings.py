"""General config and settings."""

from typing import Literal

import dotenv
from pydantic import computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict

dot_env = dotenv.find_dotenv(usecwd=True)  # type: ignore

CASE_MANAGEMENT_PREFIX = "AM_WF_"


class Settings(BaseSettings):
    """The settings for this application."""

    model_config = SettingsConfigDict(
        case_sensitive=True,  ## don't ignore case in .env/env vars
        env_file=dot_env,  ## search current and parent
        frozen=True,  ## don't allow changes
        extra="allow",  ## allow extra configs
        str_strip_whitespace=True,
        env_prefix=CASE_MANAGEMENT_PREFIX,
    )

    API_LISTEN_HOST: str = "0.0.0.0"
    API_LISTEN_PORT: int = 8000
    API_WORKERS: int = 2

    CLOUD_PROVIDER: Literal["gcp"]

    INTERACTION_NOTIFICATION_WEBHOOK_URLS: list[str] | None = None

    # GCP
    GCP_PROJECT_NAME: str | None = None
    GCP_BUCKET_NAME: str | None = None
    GCP_SERVICE_ACCOUNT_EMAIL: str | None = None
    GCP_PUBSUB_TOPIC_NAME: str | None = None

    @computed_field
    @property
    def cloud_provider_properties(self) -> dict[str, str]:
        """Cloud provider agnostic properties."""
        if self.CLOUD_PROVIDER == "gcp":
            return {
                "project_name": self.GCP_PROJECT_NAME,
                "bucket_name": self.GCP_BUCKET_NAME,
                "service_account_email": self.GCP_SERVICE_ACCOUNT_EMAIL,
                "topic": self.GCP_PUBSUB_TOPIC_NAME,
            }
        raise ValueError(f"Unsupported cloud provider: {self.CLOUD_PROVIDER}")

    @staticmethod
    def get_settings() -> "Settings":
        """Return the settings read from .env or environment."""
        return Settings()  # type: ignore


class DatabaseSettings(BaseSettings):
    """The settings for the Knowledge Management database."""

    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=dot_env,
        frozen=True,
        extra="allow",
        str_strip_whitespace=True,
    )

    DATABASE_TYPE: Literal["sqlite", "postgresql", "mysql", "sqlserver", "parquet"] = "postgresql"
    DATABASE_NAME: str
    DATABASE_USER: str
    DATABASE_PORT: int
    DATABASE_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_POOL_SIZE: int = 20
    INSTANCE_UNIX_SOCKET: str | None = None

    @classmethod
    def get_settings(cls):
        """Return the settings read from .env or environment."""
        return cls()
