-- Gen OS Agent Manager
DO
$$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'gen_os_agent_manager') THEN
    CREATE USER gen_os_agent_manager WITH PASSWORD 'gen_os_agent_manager_pass';
  END IF;
END
$$;
ALTER USER gen_os_agent_manager WITH LOGIN;

-- Main Database
SELECT 'CREATE DATABASE gen_os_agent_manager_db WITH OWNER gen_os_agent_manager ENCODING ''UTF8'' LC_COLLATE = ''C'' LC_CTYPE = ''C'' TEMPLATE template0'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'gen_os_agent_manager_db')\gexec

GRANT CONNECT ON DATABASE gen_os_agent_manager_db TO gen_os_agent_manager;
\c gen_os_agent_manager_db
REVOKE ALL ON SCHEMA public FROM PUBLIC;
GRANT USAGE ON SCHEMA public TO gen_os_agent_manager;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO gen_os_agent_manager;

-- -- Knowledge Management Database
-- SELECT 'CREATE DATABASE knowledge_management_db WITH OWNER gen_os_agent_manager ENCODING ''UTF8'' LC_COLLATE = ''C'' LC_CTYPE = ''C'' TEMPLATE template0'
-- WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'knowledge_management_db')\gexec

-- GRANT CONNECT ON DATABASE knowledge_management_db TO gen_os_agent_manager;
-- \c knowledge_management_db
-- REVOKE ALL ON SCHEMA public FROM PUBLIC;
-- GRANT USAGE ON SCHEMA public TO gen_os_agent_manager;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO gen_os_agent_manager;

-- -- Case Management Database
-- SELECT 'CREATE DATABASE case_management_db WITH OWNER gen_os_agent_manager ENCODING ''UTF8'' LC_COLLATE = ''C'' LC_CTYPE = ''C'' TEMPLATE template0'
-- WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'case_management_db')\gexec

-- GRANT CONNECT ON DATABASE case_management_db TO gen_os_agent_manager;
-- \c case_management_db
-- REVOKE ALL ON SCHEMA public FROM PUBLIC;
-- GRANT USAGE ON SCHEMA public TO gen_os_agent_manager;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO gen_os_agent_manager;

-- -- Conversational Database
-- SELECT 'CREATE DATABASE conversational_db WITH OWNER gen_os_agent_manager ENCODING ''UTF8'' LC_COLLATE = ''C'' LC_CTYPE = ''C'' TEMPLATE template0'
-- WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'conversational_db')\gexec

-- GRANT CONNECT ON DATABASE conversational_db TO gen_os_agent_manager;
-- \c conversational_db
-- REVOKE ALL ON SCHEMA public FROM PUBLIC;
-- GRANT USAGE ON SCHEMA public TO gen_os_agent_manager;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO gen_os_agent_manager;
