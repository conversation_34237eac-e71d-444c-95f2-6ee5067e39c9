"""Basic tests for the conversational package."""

import pytest
from fastapi.testclient import TestClient


def test_health_check(client: TestClient):
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "conversational"


def test_database_connection(get_session):
    """Test that database connection works."""
    # This test verifies that the database session can be created
    # The actual connection test would be done in an async test
    assert get_session is not None


@pytest.mark.asyncio
async def test_database_session_creation(get_session):
    """Test that database session can be created and used."""
    async with get_session as session:
        # Just test that we can create a session
        assert session is not None
