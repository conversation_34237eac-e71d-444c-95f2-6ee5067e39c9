## High Level Functional Overview

![Functional Overview V1](versions/v1/images/functional_overview.png)

### Core components

- **GenOS:** A unified platform for managing AI Applications.
- **Agent:** AI applications that provide workflow or conversational interfaces for business process automation and augmentation.
- **Knowledge Management:** Functionality that allows the management of documents that agents can use as knowledge.
- **Case Management:** Functionality that allows the management of documents that agents can use as knowledge.
- **Issue Management:** Functionality that allows us to keep track of issues that will be used to optimize agents.
- **Activity:** A layer for conversation / execution logging to be used for monitoring, platform reporting, agent debugging, and agent optimization purposes.
- **Job Management:** Platform used to manage start/stop of batch executions, for anything not a service including workflow agents.



## System Communication Flows

### Overview

![Communication Overview V1](/versions/v1/images/communication_overview.png)


### Interaction Flows by Use Case

#### Workflow Automation

```mermaid
sequenceDiagram
  actor Human as Human
  participant Frontend as GenOS Frontend
  participant Backend as GenOS Backend
  participant AM as Agent Manager
  participant Agent as Agent
  actor ExternalTrigger as External Trigger

  alt External Trigger
    ExternalTrigger ->> AM: Trigger workflow run
    AM ->>+ Agent: A2A: Execute first step
  else Human manually overrides step outputs
        Human ->> Frontend: Manually override step X outputs
        Frontend ->> Backend: Submit new outputs
        Backend ->> AM: New outputs for step X
        AM ->> Agent: A2A: Execute step X+1 with updated inputs
    end

  loop Workflow Execution
    Agent ->> Agent: Process step
    Agent -->> AM: A2A: Step outputs<br>+ needs_approval flag
    opt Step requires approval
      AM ->> Backend: Step needs approval
      Backend ->> Frontend: Step needs approval
      Frontend ->> Human: Show approval request
      Human ->> Frontend: Submit approval/fix outputs
      Frontend ->> Backend: Approval decision/fixed outputs
      Backend ->> AM: Approval decision/fixed outputs
    end

    AM ->> Agent: A2A: Execute next step
  end

  box GenOS
  participant Frontend
  participant Backend
  participant AM
  end
  box
  participant Agent
  end

```

#### Conversational

```mermaid
sequenceDiagram
    actor GenOSUser as GenOS User
    participant Frontend as GenOS Frontend
    participant Backend as GenOS Backend
    participant AM as Agent Manager
    participant Agent
    actor ExternalSystem

    alt Query via Frontend
        GenOSUser->>Frontend: Submit query
        Frontend->>Backend: Forward query
        Backend->>AM: A2A: Process query
        AM->>Agent: A2A: Query
        Agent->>Agent: Process query/generate response
        Agent-->>AM: A2A: Response with sources
        AM->>Backend: Return response
        Backend->>Frontend: Display to user
        Frontend->>GenOSUser: Show response & sources
    else Direct Query
        ExternalSystem->>Agent: A2A: Direct query
        Agent->>Agent: Process query/generate response
        Agent-->>ExternalSystem: A2A: Response with sources
    end

    loop Periodic Activity Logging
        Agent->>AM: A2A: Push activity logs
        AM->>Backend: Update conversation history
        Backend->>Frontend: Refresh activity display
    end

    Note right of Agent: Logs include:<br>- Conversation history<br>- Supporting sources<br>- Timestamps<br>- Confidence scores


    box GenOS
    participant Frontend
    participant Backend
    participant AM
    end
    box
    participant Agent
    end
```

## Documentation

### Workflow Configuration

- **[Workflow Configuration Guide](workflow-configuration-guide.md)** - Complete guide for creating workflow configuration files, including step types, input/output configuration, validation rules, and examples.

### Architecture Decision Records

- **[ADR Documentation](adr.md)** - Architectural decision records and design rationale.

### Technical Specifications

- **[Agent Manager Workflow Orchestration](am_cm_wf_orchestration.md)** - Technical specification for workflow orchestration in the Agent Manager.
