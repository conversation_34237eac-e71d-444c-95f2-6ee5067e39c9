"""Add constraints for order number.

Revision ID: 203b67c218e9
Revises: 21d7eb371a87
Create Date: 2025-07-16 18:33:24.202850

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "203b67c218e9"
down_revision: str | Sequence[str] | None = "21d7eb371a87"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    with op.batch_alter_table("interaction", schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f("interaction_occurrence_id_fkey"), type_="foreignkey")
        batch_op.create_foreign_key(
            None, "occurrence", ["occurrence_id"], ["id"], ondelete="CASCADE"
        )

    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.drop_constraint(batch_op.f("occurrence_step_run_id_fkey"), type_="foreignkey")
        batch_op.create_foreign_key(None, "step_run", ["step_run_id"], ["id"], ondelete="CASCADE")

    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.create_unique_constraint(
            "unique_step_order_number_per_workflow", ["workflow_id", "order_number"]
        )

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.create_unique_constraint(
            "unique_step_input_order_number", ["step_id", "order_number"]
        )

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.create_unique_constraint(
            "unique_step_output_order_number", ["step_id", "order_number"]
        )


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.create_foreign_key(
            batch_op.f("fk_workflow_agent_id"), "agents", ["agent_id"], ["id"]
        )

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.drop_constraint("unique_step_output_order_number", type_="unique")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_constraint("unique_step_input_order_number", type_="unique")

    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.drop_constraint("unique_step_order_number_per_workflow", type_="unique")

    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.drop_constraint(None, type_="foreignkey")
        batch_op.create_foreign_key(
            batch_op.f("occurrence_step_run_id_fkey"), "step_run", ["step_run_id"], ["id"]
        )

    with op.batch_alter_table("interaction", schema=None) as batch_op:
        batch_op.drop_constraint(None, type_="foreignkey")
        batch_op.create_foreign_key(
            batch_op.f("interaction_occurrence_id_fkey"), "occurrence", ["occurrence_id"], ["id"]
        )
