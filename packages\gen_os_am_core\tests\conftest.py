"""Pytest configuration and fixtures for Agent Manager Core tests.

This module provides pytest fixtures for testing the core API,
including database setup, test client configuration, and environment
variable management for the test environment.
"""

import logging
import os
import sys
import uuid
from pathlib import Path

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from sqlalchemy import text

# Add the src directory to the Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Paths for temporary on-disk SQLite databases used in tests
DATABASE_FILE = f"{Path(__file__).parent}/test_database.db"
WORKFLOWS_DATABASE_FILE = f"{Path(__file__).parent}/test_workflows.db"
KNOWLEDGE_DATABASE_FILE = f"{Path(__file__).parent}/test_knowledge.db"
CONVERSATIONAL_DATABASE_FILE = f"{Path(__file__).parent}/test_conversational.db"

# ---------------------------------------------------------------------------
# Core package variables
# ---------------------------------------------------------------------------
os.environ.setdefault("DATABASE_NAME", DATABASE_FILE)
os.environ.setdefault("DATABASE_TYPE", "sqlite")
os.environ.setdefault("DATABASE_USER", "")
os.environ.setdefault("DATABASE_PORT", "5432")
os.environ.setdefault("DATABASE_PASSWORD", "")
os.environ.setdefault("DATABASE_HOST", "")
os.environ.setdefault("AM_CORE_API_LISTEN_HOST", "localhost")
os.environ.setdefault("AM_CORE_API_LISTEN_PORT", "8080")
os.environ.setdefault("AM_CORE_API_WORKERS", "1")
os.environ.setdefault("AM_CORE_LOG_LEVEL", "DEBUG")

# ---------------------------------------------------------------------------
# Workflows package variables (gen_os_am_workflows)
# ---------------------------------------------------------------------------
os.environ.setdefault("DATABASE_NAME", WORKFLOWS_DATABASE_FILE)
os.environ.setdefault("DATABASE_TYPE", "sqlite")
os.environ.setdefault("AM_WF_CLOUD_PROVIDER", "gcp")

# ---------------------------------------------------------------------------
# Knowledge Management package variables (gen_os_am_knowledge)
# ---------------------------------------------------------------------------
os.environ.setdefault("DATABASE_NAME", KNOWLEDGE_DATABASE_FILE)
os.environ.setdefault("DATABASE_TYPE", "sqlite")
os.environ.setdefault("DATABASE_USER", "")
os.environ.setdefault("DATABASE_PASSWORD", "")
os.environ.setdefault("DATABASE_HOST", "")
os.environ.setdefault("DATABASE_PORT", "5432")

# ---------------------------------------------------------------------------
# Conversational package variables (gen_os_am_conversational)
# ---------------------------------------------------------------------------
os.environ.setdefault("DATABASE_NAME", CONVERSATIONAL_DATABASE_FILE)
os.environ.setdefault("DATABASE_TYPE", "sqlite")

# ---------------------------------------------------------------------------
# End of environment setup
# ---------------------------------------------------------------------------

# Import modules AFTER config fixture is available
from gen_os_sdk_emulator.core.database.session import SessionManager  # noqa: E402

from gen_os_am_core.api import AgentManagerAPI  # noqa: E402
from gen_os_am_core.database.models.base import Base  # noqa: E402
from gen_os_am_core.settings import DatabaseSettings  # noqa: E402

db_settings = DatabaseSettings.get_settings()


@pytest_asyncio.fixture(scope="session")
async def config():
    """Configure test environment variables."""
    # Simply expose the database file path (already configured above) so that
    # other session-scoped fixtures can use it for teardown.
    return {"database_file": DATABASE_FILE}


@pytest_asyncio.fixture(scope="session")
async def create_tables(config):
    """Create database tables for testing."""
    database_file = config["database_file"]

    # Remove existing database file if it exists to start fresh
    if Path(database_file).exists():
        os.remove(database_file)

    # Create tables
    async with SessionManager.get_session(db_settings) as db_session:
        # Drop all tables first
        await (await db_session.connection()).run_sync(
            lambda sync_conn: Base.metadata.drop_all(sync_conn)
        )
        # Then create tables
        await (await db_session.connection()).run_sync(
            lambda sync_conn: Base.metadata.create_all(sync_conn)
        )
        yield

    # Cleanup: Remove database file after all tests
    if Path(database_file).exists():
        os.remove(database_file)


@pytest_asyncio.fixture(scope="function", autouse=True)
async def clean_database(create_tables):
    """Clean database before each test to ensure isolation."""
    async with SessionManager.get_session(db_settings) as db_session:
        # Clear all data from tables before each test
        await db_session.execute(text("DELETE FROM reported_incidents_conversations"))
        await db_session.execute(text("DELETE FROM reported_incidents_workflows"))
        await db_session.execute(text("DELETE FROM issue_logs"))
        await db_session.execute(text("DELETE FROM agent_configuration"))
        await db_session.execute(text("DELETE FROM issue"))
        await db_session.execute(text("DELETE FROM agents"))
        await db_session.commit()
    # Yield control back to the test
    yield


@pytest.fixture(scope="function")
def client(clean_database):
    """Test client fixture with clean database."""
    logger = logging.getLogger("api")
    logger.level = logging.DEBUG

    # Create API instance and FastAPI app
    am_api = AgentManagerAPI(logger=logger)
    app = am_api.create_api()

    with TestClient(app) as client:
        yield client


@pytest_asyncio.fixture
async def get_session():
    """Get a database session for testing."""
    return SessionManager.get_session


@pytest.fixture
def test_agent_id():
    """Test agent ID fixture."""
    return "test-agent-123"


@pytest_asyncio.fixture
async def create_test_agent(test_agent_id):
    """Create a test agent."""
    from gen_os_am_core.database.models.agents import Agent

    async with SessionManager.get_session(db_settings) as db:
        agent = Agent(
            id=test_agent_id,
            name="Test Agent",
            url="http://test-agent:8000",
            description="Test agent for API tests",
        )
        db.add(agent)
        await db.commit()
        yield test_agent_id


@pytest.fixture
def test_config_data():
    """Sample configuration data for testing."""
    return {
        "id": uuid.uuid4(),
        "name": "test_parameter",
        "description": "A test configuration parameter",
        "value": "test_value",
    }


@pytest.fixture
def test_sync_request_data():
    """Sample sync request data for testing."""
    return {
        "configurations": [
            {
                "id": str(uuid.uuid4()),
                "name": "param1",
                "description": "First parameter",
                "value": "value1",
            },
            {
                "id": str(uuid.uuid4()),
                "name": "param2",
                "description": "Second parameter",
                "value": "value2",
            },
        ]
    }
