"""General config and settings for Conversational Package."""

from typing import Literal

import dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

dot_env = dotenv.find_dotenv(usecwd=True)  # type: ignore

AM_PREFIX = "AM_CONV_"


class Settings(BaseSettings):
    """The settings for the Conversational package - uses Agent Manager database."""

    model_config = SettingsConfigDict(
        case_sensitive=True,  ## don't ignore case in .env/env vars
        env_file=dot_env,  ## search current and parent
        frozen=True,  ## don't allow changes
        extra="allow",  ## allow extra configs
        str_strip_whitespace=True,
        env_prefix=AM_PREFIX,
    )

    API_LISTEN_HOST: str = "0.0.0.0"
    API_LISTEN_PORT: int = 8080
    API_WORKERS: int = 2

    @staticmethod
    def get_settings() -> "Settings":
        """Return the settings read from .env or environment."""
        return Settings()  # type: ignore


class DatabaseSettings(BaseSettings):
    """The settings for the Conversational database."""

    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=dot_env,
        frozen=True,
        extra="allow",
        str_strip_whitespace=True,
    )

    DATABASE_TYPE: Literal["sqlite", "postgresql", "mysql", "sqlserver", "parquet"] = "postgresql"
    DATABASE_NAME: str
    DATABASE_USER: str
    DATABASE_PORT: int
    DATABASE_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_POOL_SIZE: int = 20
    INSTANCE_UNIX_SOCKET: str | None = None

    @classmethod
    def get_settings(cls):
        """Return the settings read from .env or environment."""
        return cls()
