"""create core branch.

Revision ID: 72266d413d36
Revises:
Create Date: 2025-07-16 14:32:54.782363

"""

from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "72266d413d36"
down_revision: str | Sequence[str] | None = "9e947542e19f"
branch_labels: str | Sequence[str] | None = ("core",)
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
