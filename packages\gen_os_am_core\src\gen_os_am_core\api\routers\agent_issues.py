"""FastAPI router for issue creation and management endpoints."""

import logging
import uuid
from collections.abc import Callable
from datetime import datetime
from functools import partial
from typing import get_args

from fastapi import APIRouter, Depends, HTTPException, Path, Query, Request, Response
from gen_os_sdk_emulator.core.database.session import get_session
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from gen_os_am_core.api.schemas import (
    AddIncidentsRequest,
    IncidentSoftDeleteRequest,
    IncidentSoftDeleteResponse,
    IssueCreateRequest,
    IssueListFilters,
    IssueListItem,
    IssueLogPatchRequest,
    IssueLogResponse,
    IssueLogsResponse,
    IssueResponse,
    IssueUpdateRequest,
    IssueUpdateResponse,
    IssueWithIncidents,
    PaginatedIssuesResponse,
)
from gen_os_am_core.api.utils.issue_service import (
    add_incidents_to_issue,
    edit_issue_log,
    fetch_issue_incidents,
    get_issue_logs,
    soft_delete_issue_log,
    fetch_issue_record,
    get_agent_issues_paginated,
    get_issue_summary,
    soft_delete_restore_incident,
    update_issue_fields,
)
from gen_os_am_core.database.models.agent_issue import (
    Issue,
    ReportedIncidentConversation,
    ReportedIncidentWorkflow,
)
from gen_os_am_core.models.enums import IssueCategory, IssueState
from gen_os_am_core.settings import DatabaseSettings

db_session: Callable = partial(get_session, DatabaseSettings.get_settings())


class AgentIssuesRouter:
    """Agent issues router for the Agent Manager API."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the agent issues router.

        Args:
            logger: Optional logger instance

        """
        self.logger = logger or logging.getLogger(__name__)

        # Router for all agent-scoped operations (with agent_id)
        self.router_agent_scoped = APIRouter(
            prefix="/agents/{agent_id}/core/v1",
            dependencies=[Depends(self._agent_id_dependency)],
            tags=["Agent Issues"],
        )

        self._setup_routes()

    @staticmethod
    def _agent_id_dependency(agent_id: str = Path(...)):
        """Extract agent_id from the path.

        This allows us to add agent_id to the router prefix without
        adding it to every endpoint signature.
        """
        return agent_id

    def _setup_routes(self):
        """Set up the agent issues routes."""

        @self.router_agent_scoped.post(
            "/issue",
            response_model=IssueResponse,
            summary="Create a new issue",
            description="""Create a new issue with optional
            related conversation and workflow incidents.""",
            status_code=201,
        )
        async def create_issue(
            request: Request,
            issue_request: IssueCreateRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Create a new issue and related incidents for a specific agent.

            Args:
                request: FastAPI request object used to extract path params and headers.
                issue_request: Parsed request body containing issue details.
                db: Database session

            Returns:
                IssueResponse: The created issue with its nested incidents.

            """
            agent_id = request.path_params["agent_id"]
            created_by = request.headers.get("X-User-ID")

            try:
                # Create Issue instance
                issue = Issue(
                    agent_id=agent_id,
                    description=issue_request.description,
                    issue_type=issue_request.issue_type,
                    state=issue_request.state,
                )

                # Create related conversation incidents
                for conv in issue_request.reported_incidents_conversations:
                    incident_conv = ReportedIncidentConversation(
                        conversation_id=conv.conversation_id,
                        answer=conv.answer,
                        description=conv.description,
                        severity=conv.severity,
                        created_by=created_by,
                    )
                    issue.reported_incidents_conversations.append(incident_conv)

                # Create related workflow incidents
                for wf in issue_request.reported_incidents_workflows:
                    incident_wf = ReportedIncidentWorkflow(
                        workflow_execution_id=wf.workflow_execution_id,
                        step=wf.step,
                        description=wf.description,
                        severity=wf.severity,
                        created_by=created_by,
                    )
                    issue.reported_incidents_workflows.append(incident_wf)

                # Persist to database
                db.add(issue)
                await db.commit()

                await db.refresh(
                    issue,
                    [
                        "reported_incidents_conversations",
                        "reported_incidents_workflows",
                    ],
                )

                return IssueResponse.model_validate(issue)

            except IntegrityError as e:
                self.logger.warning(f"Integrity error creating issue: {e}")
                raise HTTPException(
                    status_code=409,
                    detail="Issue creation failed due to constraint violation.",
                ) from e
            except Exception as e:
                self.logger.error("Unexpected error in create_issue", exc_info=True)
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router_agent_scoped.get(
            "/issue/{issue_id}",
            response_model=IssueWithIncidents,
            summary="Get a specific issue by ID",
            description="""Retrieve full details for a single issue,
            including its reported incidents.""",
        )
        async def get_issue_detail(
            request: Request,
            issue_id: uuid.UUID,
            db: AsyncSession = Depends(db_session),
        ):
            """Retrieve one issue with its incidents."""
            agent_id = request.path_params["agent_id"]

            try:
                issue_row = await fetch_issue_record(db, issue_id, agent_id)
                if issue_row is None:
                    raise HTTPException(
                        status_code=404,
                        detail=f"""Issue with ID {issue_id}
                        not found for agent {agent_id}""",
                    )

                incidents = await fetch_issue_incidents(
                    db=db,
                    issue_id=issue_id,
                    incident_type="all",
                    incident_sort="desc",
                )

                response_payload = IssueWithIncidents(
                    id=issue_row.id,
                    description=issue_row.description,
                    issue_type=issue_row.issue_type,
                    state=issue_row.state,
                    close_desc=issue_row.close_desc,
                    agent=issue_row.agent_id,
                    created_at=issue_row.created_at,
                    updated_at=issue_row.updated_at,
                    reported_incidents=incidents,
                )

                return response_payload

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in get_issue_detail: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.patch(
            "/incident/{incident_id}",
            response_model=IncidentSoftDeleteResponse,
            summary="Soft delete or restore an incident",
            description="""Mark an incident as deleted (soft delete) or restore it
            by toggling the `is_deleted` flag.""",
        )
        async def soft_delete_incident(
            request: Request,
            incident_id: uuid.UUID,
            payload: IncidentSoftDeleteRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Soft delete (or restore) a conversation/workflow incident."""
            agent_id = request.path_params["agent_id"]

            try:
                incident, kind = await soft_delete_restore_incident(
                    db=db,
                    agent_id=agent_id,
                    incident_id=incident_id,
                    payload=payload,
                )

                return IncidentSoftDeleteResponse(
                    id=incident.id,
                    is_deleted=incident.is_deleted,
                    deleted_at=incident.deleted_at,
                    deleted_by=incident.deleted_by,
                    kind=kind,
                )
            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in soft_delete_incident", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.patch(
            "/issue/{issue_id}",
            response_model=IssueUpdateResponse,
            summary="Update an issue",
            description="""Update issue fields like description, issue_type,
            state, and close_desc.""",
        )
        async def update_issue(
            request: Request,
            issue_id: uuid.UUID,
            update_data: IssueUpdateRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Update an existing issue for a specific agent."""
            agent_id = request.path_params["agent_id"]

            try:
                issue = await update_issue_fields(
                    db=db,
                    agent_id=agent_id,
                    issue_id=issue_id,
                    update_data=update_data,
                )
                return IssueUpdateResponse.model_validate(issue)

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in update_issue: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.get(
            "/issues",
            response_model=PaginatedIssuesResponse,
            summary="List issues for a specific agent",
            description="""List issues for a specific
            agent with advanced filtering and pagination.""",
        )
        async def list_agent_issues(
            request: Request,
            response: Response,
            filters: IssueListFilters = Query(),
            db: AsyncSession = Depends(db_session),
        ):
            """List issues for a specific agent with filtering and pagination."""
            agent_id = request.path_params["agent_id"]

            try:
                issue_records, pagination = await get_agent_issues_paginated(
                    db=db,
                    agent_id=agent_id,
                    filters=filters,
                )

                issue_items = [IssueListItem(**rec) for rec in issue_records]

                # Include summary only when showing open issues
                summary_payload = None
                if filters.is_open:
                    summary_payload = await get_issue_summary(db, agent_id)

                response.headers["X-Total-Count"] = str(pagination.total_items)

                return PaginatedIssuesResponse(
                    items=issue_items,
                    pagination=pagination,
                    summary=summary_payload,
                )

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in list_agent_issues: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router_agent_scoped.get(
            "/issue-state",
            response_model=list[str],
            summary="Get possible issue states for agent",
            description="""Retrieve a list of all possible
            states for an issue in the context of a specific agent.""",
        )
        async def get_agent_issue_states(request: Request) -> list[str]:
            """Return all possible states for an issue for a specific agent."""
            # agent_id is available via request.path_params["agent_id"]
            # if needed for future logic
            return list(get_args(IssueState))

        @self.router_agent_scoped.get(
            "/issue-types",
            response_model=list[str],
            summary="Get possible issue types for agent",
            description="""Retrieve a list of all possible types for
            an issue in the context of a specific agent.""",
        )
        async def get_agent_issue_types(request: Request) -> list[str]:
            """Return all possible types for an issue for a specific agent."""
            # agent_id is available via request.path_params["agent_id"]
            # if needed for future logic
            return list(get_args(IssueCategory))

        @self.router_agent_scoped.post(
            "/issue/{issue_id}/incidents",
            response_model=IssueResponse,
            summary="Add incidents to an existing issue",
            description="""Add new cnv adn wf incidents to an existing issue.
            This endpoint allows adding incidents into an existing issue without
            modifying the issue's core properties.""",
            status_code=201,
        )
        async def add_incidents_to_existing_issue(
            request: Request,
            issue_id: uuid.UUID,
            incidents_request: AddIncidentsRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Add new incidents to an existing issue for a specific agent.

            Args:
                request: FastAPI request object used to extract path params and headers.
                issue_id: UUID of the existing issue to add incidents to.
                incidents_request: Parsed request body containing incidents to add.
                db: Database session dependency.

            Returns:
                IssueResponse: The updated issue with all incidents (existing + new).

            """
            agent_id = request.path_params["agent_id"]
            created_by = request.headers.get("X-User-ID")

            try:
                issue = await add_incidents_to_issue(
                    db,
                    agent_id=agent_id,
                    issue_id=issue_id,
                    reported_incidents_conversations=incidents_request.reported_incidents_conversations,
                    reported_incidents_workflows=incidents_request.reported_incidents_workflows,
                    created_by=created_by,
                )
                return IssueResponse.model_validate(issue)

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except IntegrityError as e:
                self.logger.warning(f"Integrity error adding incidents: {e}")
                raise HTTPException(
                    status_code=409,
                    detail="Adding incidents failed due to constraint violation.",
                ) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in add_incidents_to_existing_issue", exc_info=True
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router_agent_scoped.get(
            "/issue/{issue_id}/logs",
            response_model=IssueLogsResponse,
            summary="Get issue logs (events timeline)",
            description="""Retrieve the events timeline for a specific issue,
            showing all log entries like creation, state changes, field edits,
            incident additions/removals, and comments.""",
        )
        async def get_issue_logs_endpoint(
            request: Request,
            issue_id: uuid.UUID,
            limit: int = Query(50, ge=1, le=100, description="Maximum number of logs to return"),
            offset: int = Query(0, ge=0, description="Number of logs to skip"),
            db: AsyncSession = Depends(db_session),
        ):
            """Get issue logs (events timeline) for a specific issue.

            Args:
                request: FastAPI request object used to extract path params.
                issue_id: UUID of the issue to get logs for.
                limit: Maximum number of logs to return (1-100).
                offset: Number of logs to skip for pagination.
                db: Database session dependency.

            Returns:
                IssueLogsResponse: List of log entries and total count.

            """
            agent_id = request.path_params["agent_id"]

            try:
                logs, total_count = await get_issue_logs(
                    db,
                    agent_id=agent_id,
                    issue_id=issue_id,
                    limit=limit,
                    offset=offset,
                )

                log_responses = [IssueLogResponse.from_issue_log(log) for log in logs]

                return IssueLogsResponse(
                    logs=log_responses,
                    total_count=total_count,
                )

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in get_issue_logs_endpoint", exc_info=True
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router_agent_scoped.patch(
            "/issue/{issue_id}/logs/{log_id}/comme",
            response_model=IssueLogResponse,
            summary="Edit or delete an issue log entry",
            description="""Edit or soft-delete an issue log entry (currently only supports comments).
            Use action='edit' with new_comment to edit, or action='delete' to soft-delete.""",
        )
        async def patch_issue_log_endpoint(
            request: Request,
            issue_id: uuid.UUID,
            log_id: uuid.UUID,
            patch_data: IssueLogPatchRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Edit or soft-delete an issue log entry.

            Args:
                request: FastAPI request object used to extract path params and headers.
                issue_id: UUID of the issue the log belongs to.
                log_id: UUID of the log entry to modify.
                patch_data: PATCH request data with action and optional new_comment.
                db: Database session dependency.

            Returns:
                IssueLogResponse: The updated log entry.

            """
            agent_id = request.path_params["agent_id"]
            user_id = request.headers.get("X-User-ID")

            try:
                if patch_data.action == "edit":
                    log_entry = await edit_issue_log(
                        db,
                        agent_id=agent_id,
                        issue_id=issue_id,
                        log_id=log_id,
                        new_comment=patch_data.new_comment,
                        edited_by=user_id,
                    )
                elif patch_data.action == "delete":
                    log_entry = await soft_delete_issue_log(
                        db,
                        agent_id=agent_id,
                        issue_id=issue_id,
                        log_id=log_id,
                        deleted_by=user_id,
                    )
                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid action: {patch_data.action}",
                    )

                return IssueLogResponse.from_issue_log(log_entry)

            except ValueError as e:
                # Handle validation errors (not found, permission denied, etc.)
                error_msg = str(e)
                if "not found" in error_msg:
                    raise HTTPException(status_code=404, detail=error_msg) from e
                elif "permission" in error_msg.lower() or "only the author" in error_msg:
                    raise HTTPException(status_code=403, detail=error_msg) from e
                else:
                    raise HTTPException(status_code=400, detail=error_msg) from e
            except Exception as e:
                self.logger.error(
                    "Unexpected error in patch_issue_log_endpoint", exc_info=True
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

    def get_router(self) -> APIRouter:
        """Get the configured router.

        Returns:
            The agent-scoped router with all the issue management routes.

        """
        return self.router_agent_scoped
