"""Tests for the workflow execution endpoints."""

import uuid

from fastapi.testclient import TestClient


class TestWorkflowExecutionEndpoints:
    """Test workflow execution CRUD operations."""

    def test_create_workflow_execution(self, client: TestClient):
        """Test creating a new workflow execution."""
        # First create a workflow
        workflow_name = f"test_workflow_exec_{uuid.uuid4().hex[:8]}"
        agent_id = f"test-agent-{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for executions",
            "extra_fields": [],
            "agent_id": agent_id,
        }

        create_workflow_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Then create an execution
        execution_data = {"status": "running"}

        response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions",
            json=execution_data,
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "running"
        assert "id" in data

    def test_get_workflow_executions(self, client: TestClient):
        """Test getting workflow executions."""
        workflow_name = f"test_workflow_exec_list_{uuid.uuid4().hex[:8]}"
        agent_id = f"test-agent-{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for listing executions",
            "extra_fields": [],
            "agent_id": agent_id,
        }

        create_workflow_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Create a few executions
        for _ in range(3):
            execution_data = {"status": "running"}
            client.post(
                f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions",
                json=execution_data,
            )

        # Get all executions
        response = client.get(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions"
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 3

    def test_get_workflow_execution_by_id(self, client: TestClient):
        """Test getting a specific workflow execution by ID."""
        # First create a workflow
        workflow_name = f"test_workflow_exec_get_{uuid.uuid4().hex[:8]}"
        agent_id = f"test-agent-{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for getting executions",
            "extra_fields": [],
            "agent_id": agent_id,
        }

        create_workflow_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Create an execution
        execution_data = {"status": "running"}
        create_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions",
            json=execution_data,
        )
        assert create_response.status_code == 200
        execution_id = create_response.json()["id"]

        # Get it by ID
        response = client.get(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions/{execution_id}"
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == execution_id
        assert data["status"] == "running"

    def test_update_workflow_execution(self, client: TestClient):
        """Test updating a workflow execution."""
        # First create a workflow
        workflow_name = f"test_workflow_exec_update_{uuid.uuid4().hex[:8]}"
        agent_id = f"test-agent-{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for updating executions",
            "extra_fields": [],
            "agent_id": agent_id,
        }

        create_workflow_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Then create an execution
        execution_data = {"status": "running"}

        create_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions",
            json=execution_data,
        )
        assert create_response.status_code == 200
        execution_id = create_response.json()["id"]

        # Then update it
        update_data = {
            "status": "completed",
            "extra_fields": [{"field_name": "result", "field_type": "string", "data": "success"}],
        }

        response = client.patch(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions/{execution_id}",
            json=update_data,
        )

        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "successfully" in data["message"].lower()

        # Verify the update
        get_response = client.get(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions/{execution_id}"
        )
        assert get_response.status_code == 200
        get_data = get_response.json()
        assert get_data["status"] == "completed"
        assert get_data["extra_fields"][0]["data"] == "success"

    def test_delete_workflow_execution(self, client: TestClient):
        """Test deleting a workflow execution."""
        # First create a workflow
        workflow_name = f"test_workflow_exec_delete_{uuid.uuid4().hex[:8]}"
        agent_id = f"test-agent-{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for deleting executions",
            "extra_fields": [],
            "agent_id": agent_id,
        }

        create_workflow_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Then create an execution
        execution_data = {"status": "running"}

        create_response = client.post(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions",
            json=execution_data,
        )
        assert create_response.status_code == 200
        execution_id = create_response.json()["id"]

        # Then delete it
        response = client.delete(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions/{execution_id}"
        )
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "successfully" in data["message"].lower()

        # Verify it's deleted
        get_response = client.get(
            f"/agents/{agent_id}/wf/v1/workflows/{workflow_name}/workflow-executions/{execution_id}"
        )
        assert get_response.status_code == 404
