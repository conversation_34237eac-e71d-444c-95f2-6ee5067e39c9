"""Pytest configuration and fixtures for KM tests."""

import asyncio
import os
import shutil
import sys
import threading
import time
import urllib.parse
from unittest.mock import MagicMock

import requests

if sys.platform.startswith("win"):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# noqa: E402 - Imports are after sys.path modification by design
import pytest
import uvicorn
from dotenv import load_dotenv
from fastapi import APIRouter, FastAPI
from fastapi.responses import Response
from fastapi.testclient import TestClient
from pytest import fixture


# Use pytest_configure to set environment variables before test collection
def pytest_configure(config):
    """Load environment variables before test collection."""
    load_dotenv(dotenv_path=".env")

    # --- Standardized Database Configuration for Tests ---
    db_name = "knowledge_management_db"
    db_user = "gen_os_agent_manager"
    db_pass = "gen_os_agent_manager_pass"
    db_host = os.getenv("TEST_DATABASE_HOST", "127.0.0.1")
    db_port = os.getenv("TEST_DATABASE_PORT", "5432")

    # Explicitly set DATABASE_* vars for the test session
    os.environ["DATABASE_NAME"] = db_name
    os.environ["DATABASE_USER"] = db_user
    os.environ["DATABASE_PASSWORD"] = db_pass
    os.environ["DATABASE_HOST"] = db_host
    os.environ["DATABASE_PORT"] = str(db_port)

    # Set AM_KM_ prefixed
    os.environ["DATABASE_NAME"] = db_name
    os.environ["DATABASE_USER"] = db_user
    os.environ["DATABASE_PASSWORD"] = db_pass
    os.environ["DATABASE_HOST"] = db_host
    os.environ["DATABASE_PORT"] = str(db_port)
    os.environ["AM_KM_SQLALCHEMY_POOL_SIZE"] = "20"

    # Keep the POSTGRES_* vars aligned for CI/other potential consumers
    os.environ["POSTGRES_DB"] = db_name
    os.environ["POSTGRES_USER"] = db_user
    os.environ["POSTGRES_PASSWORD"] = db_pass
    os.environ["POSTGRES_HOST"] = db_host
    os.environ["POSTGRES_PORT"] = str(db_port)

    # --- End Standardized DB Config ---

    test_storage_path = "test_storage"
    os.makedirs(test_storage_path, exist_ok=True)
    os.environ["LOCAL_STORAGE_PATH"] = test_storage_path
    os.environ["AM_KM_LOCAL_STORAGE_PATH"] = test_storage_path

    # Set Agent Manager settings - required for AMSettings
    agent_url = "http://localhost:8084/"
    agent_prefix = "test-agent"
    os.environ["AGENT_API_URL"] = agent_url + agent_prefix
    os.environ["AM_KM_AGENT_API_URL"] = agent_url + agent_prefix
    os.environ["AM_AGENT_URL"] = agent_url
    os.environ["AM_AGENT_PREFIX"] = agent_prefix

    # API settings for the test environment
    os.environ["API_PREFIX"] = os.getenv("TEST_API_PREFIX", "/")
    os.environ["API_PORT"] = os.getenv("TEST_API_PORT", "8080")
    os.environ["API_HOST"] = os.getenv("TEST_API_HOST", "localhost")
    os.environ["ALLOWED_ORIGINS"] = os.getenv("TEST_ALLOWED_ORIGINS", '["*"]')
    os.environ["API_WORKERS"] = os.getenv("TEST_API_WORKERS", "4")
    os.environ["API_RELOAD"] = os.getenv("TEST_API_RELOAD", "false")

    # Other shared settings or defaults for the test environment
    os.environ["DEBUG"] = os.getenv("DEBUG", "True")
    os.environ["ENVIRONMENT"] = os.getenv(
        "ENVIRONMENT", "DEV"
    )  # Keep as DEV for testing usually
    os.environ["AM_KM_LOG_LEVEL"] = os.getenv("LOG_LEVEL", "ERROR")
    os.environ["PROJECT_NAME"] = os.getenv("PROJECT_NAME", "knowledge_management")
    os.environ["AM_DRAFT_MODE"] = os.getenv("AM_DRAFT_MODE", "false")


@pytest.fixture(scope="session")
def define_test_settings():
    """Define any additional test-specific settings if needed."""
    settings = {
        # Add any additional test settings here if needed
        # AM_KM_LOCAL_STORAGE_PATH is now set in pytest_configure
    }
    # Set any remaining settings that weren't covered by pytest_configure
    for k, v in settings.items():
        if k not in os.environ:
            os.environ[k] = v


@fixture(scope="session", autouse=True)
def agent_api(define_test_settings):
    """Set up the agent API."""
    prefix = (
        "/"
        + os.getenv("AM_KM_AGENT_API_URL", "http://localhost:8084/test-agent").split(
            "/"
        )[-1]
        + "/vector-store"
    )
    vector_store_router = APIRouter(prefix=prefix)

    @vector_store_router.post("/create")
    async def new_docs() -> Response:
        return Response(status_code=204)

    @vector_store_router.patch("/update")
    async def update_doc() -> Response:
        return Response(status_code=204)

    @vector_store_router.delete("/delete")
    async def delete_doc() -> Response:
        return Response(status_code=204)

    @vector_store_router.post("/publish")
    async def publish_docs() -> Response:
        return Response(status_code=204)

    @vector_store_router.post("/restore")
    async def draft_docs() -> Response:
        return Response(status_code=204)

    app = FastAPI()
    app.include_router(vector_store_router)

    parsed_url = urllib.parse.urlparse(
        os.getenv("AM_KM_AGENT_API_URL", "http://localhost:8084/test-agent")
    )
    host = parsed_url.hostname
    port = parsed_url.port

    server_thread = threading.Thread(
        target=uvicorn.run,
        args=(app,),
        kwargs={"host": host, "port": port},
        daemon=True,
    )
    server_thread.start()

    # Try to reach the mock agent API endpoint
    time.sleep(3)  # Give the server a moment to start

    yield app


@pytest.fixture(scope="session")
def create_root_folder():
    """Create the root folder."""
    storage_path = os.environ["AM_KM_LOCAL_STORAGE_PATH"]
    root_folder = "root-folder"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    # Clean up after tests
    if os.path.exists(storage_path):
        shutil.rmtree(storage_path)


@pytest.fixture(scope="session")
def mock_am_settings():
    """Mock Agent Manager settings for tests."""
    mock_settings = MagicMock()
    mock_settings.AGENT_URL = os.getenv("AM_AGENT_URL", "http://localhost:8084/")
    mock_settings.AGENT_PREFIX = os.getenv("AM_AGENT_PREFIX", "test-agent")
    return mock_settings


@pytest.fixture(scope="session")
def setup_api(create_root_folder):
    """Set up the API. Imports are deferred to here."""
    # Import necessary modules *after* env vars are set
    from gen_os_am_knowledge.api import (
        DocumentAPI,
        FolderAPI,
        IngestionAPI,
        SourceAPI,
        TagAPI,
        UserAPI,
    )
    from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
        LocalFSConnector,
    )

    local_storage_connector = LocalFSConnector(root_folder=create_root_folder)

    tag_api_router = TagAPI().get_router()
    document_api_router = DocumentAPI(
        storage_connector=local_storage_connector
    ).get_router()
    source_api_router = SourceAPI().get_router()
    folder_api_router = FolderAPI(fs_connector=local_storage_connector).get_router()
    user_api_router = UserAPI().get_router()
    ingestion_api_router = IngestionAPI().get_router()

    app = FastAPI()
    app.include_router(tag_api_router)
    app.include_router(document_api_router)
    app.include_router(source_api_router)
    app.include_router(folder_api_router)
    app.include_router(user_api_router)
    app.include_router(ingestion_api_router)

    return app


@pytest.fixture(scope="session")
def setup_database():
    """Set up the database: create tables, run populate script, and add defaults."""
    import pathlib

    # Use KM's new SessionManager
    from gen_os_sdk_emulator.core.database.session import SessionManager
    from sqlmodel import SQLModel

    from gen_os_am_knowledge.config.settings import DatabaseSettings
    from gen_os_am_knowledge.sdk.models import (
        Document,
        DocumentTags,
        Folder,
        Ingestion,
        Source,
        Tag,
        User,
    )
    from gen_os_am_knowledge.sdk.models.common import (
        DEFAULT_FOLDER,
        DEFAULT_INGESTION,
        DEFAULT_SOURCE,
        DEFAULT_USER,
    )

    db_settings = DatabaseSettings.get_settings()

    # Use sync session for database setup
    with SessionManager.get_sync_session(db_settings) as session:
        # Drop existing tables first for a clean slate
        with session.connection() as conn:
            SQLModel.metadata.drop_all(conn)
            # Create tables
            SQLModel.metadata.create_all(
                conn,
                tables=[
                    Document.__table__,
                    Tag.__table__,
                    DocumentTags.__table__,
                    Ingestion.__table__,
                    User.__table__,
                    Source.__table__,
                    Folder.__table__,
                ],
            )
            session.commit()

        # Add default data
        try:
            session.add(DEFAULT_FOLDER)
            session.add(DEFAULT_SOURCE)
            session.add(DEFAULT_INGESTION)
            session.add(DEFAULT_USER)
            session.commit()
        except Exception as e:
            session.rollback()
            raise e

        yield  # Database is ready for testing


@pytest.fixture(scope="session")
def client(setup_api, setup_database):
    """Create a test client for the API."""
    # Make sure TestClient uses the app created by setup_api
    # setup_database ensures the database is ready
    with TestClient(setup_api) as c:
        yield c
