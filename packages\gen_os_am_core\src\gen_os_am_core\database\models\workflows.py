"""SQLAlchemy models for workflow datasets."""

import uuid
from datetime import datetime
from typing import Any, Literal

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Integer, Sequence, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.models.base import Base

StateType = Literal["event", "context"]
StateTypeContext: StateType = "context"
StateTypeEvent: StateType = "event"


class WorkflowDataset(Base):
    """SQLAlchemy model for workflow datasets."""

    __tablename__ = "workflow_datasets"

    # TODO(Henrique): Add a unique constraint (name, agent_id)

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    handle: Mapped[int] = mapped_column(
        Integer,
        Sequence(name="workflow_datasets_handle_seq", start=1, increment=1),
        nullable=False,
        unique=True,
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.now, onupdate=datetime.now
    )
    agent_id: Mapped[str] = mapped_column(
        String, ForeignKey("agents.id"), nullable=False
    )
    inputs: Mapped[list[dict[str, Any]]] = mapped_column(JSON, nullable=False)

    # Relationship to Agent
    agent: Mapped["Agent"] = relationship("Agent", foreign_keys=[agent_id])


class WorkflowExecutionState(Base):
    """Database table for storing workflow execution state."""

    __tablename__ = "workflow_execution_state"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    execution_id: Mapped[str] = mapped_column(String, nullable=False)
    state_type: Mapped[StateType] = mapped_column(String, nullable=False)
    state_value: Mapped[str | None] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.now, onupdate=datetime.now
    )
