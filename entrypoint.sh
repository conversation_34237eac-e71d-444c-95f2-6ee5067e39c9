#!/bin/bash

# Activate PDM virtual environment
echo "Activating PDM virtual environment..."
cd /home/<USER>/packages/gen_os_am_core && source .venv/bin/activate

# Run all migrations
echo "Running all database migrations..."
cd /home/<USER>/packages/gen_os_am_core/db && alembic upgrade heads

# Start the application
echo "Starting the application..."
cd /home/<USER>/packages/gen_os_am_core/src && uvicorn gen_os_am_core.main:am_app --host 0.0.0.0 --port 8080
