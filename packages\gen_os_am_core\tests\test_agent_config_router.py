"""Tests for the agent configuration router."""

import uuid

import pytest
from gen_os_sdk_emulator.core.database.session import <PERSON><PERSON><PERSON>ger
from sqlalchemy import select

from gen_os_am_core.database.models.agent_configuration import AgentConfiguration
from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.settings import DatabaseSettings

db_settings = DatabaseSettings.get_settings()


class TestAgentConfigRouter:
    """Test cases for the agent configuration router."""

    @pytest.fixture
    def test_agent_id(self):
        """Test agent ID fixture."""
        return "test-agent-123"

    @pytest.fixture
    def test_config_data(self, test_agent_id):
        """Test configuration data fixture."""
        return {
            "id": str(uuid.uuid4()),
            "name": "test_parameter",
            "description": "A test configuration parameter",
            "value": "test_value",
        }

    @pytest.fixture
    def test_sync_request_data(self):
        """Test sync request data fixture."""
        return {
            "configurations": [
                {
                    "id": str(uuid.uuid4()),
                    "name": "param1",
                    "description": "First parameter",
                    "value": "value1",
                },
                {
                    "id": str(uuid.uuid4()),
                    "name": "param2",
                    "description": "Second parameter",
                    "value": "value2",
                },
            ]
        }

    @pytest.mark.asyncio
    async def test_list_agent_configurations_empty(self, client, test_agent_id):
        """Test listing configurations for agent with no configurations."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

        response = client.get(f"/agents/{test_agent_id}/core/v1/agent-configuration")
        assert response.status_code == 200
        assert response.json() == []

    @pytest.mark.asyncio
    async def test_list_agent_configurations_with_data(
        self, client, test_agent_id, test_config_data
    ):
        """Test listing configurations for agent with existing configurations."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

            # Create a test configuration in the database
            config = AgentConfiguration(
                id=str(test_config_data["id"]),
                agent_id=test_agent_id,
                name=test_config_data["name"],
                description=test_config_data["description"],
                value=test_config_data["value"],
            )
            db.add(config)
            await db.commit()

        response = client.get(f"/agents/{test_agent_id}/core/v1/agent-configuration")
        assert response.status_code == 200
        configs = response.json()
        assert len(configs) == 1
        assert configs[0]["id"] == str(test_config_data["id"])
        assert configs[0]["name"] == test_config_data["name"]
        assert configs[0]["value"] == test_config_data["value"]

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_create_new(
        self, client, test_agent_id, test_sync_request_data
    ):
        """Test syncing configurations that creates new configurations."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/agent-configuration",
            json=test_sync_request_data,
        )
        assert response.status_code == 200
        configs = response.json()
        assert len(configs) == len(test_sync_request_data["configurations"])

        # Verify configurations were created
        async with SessionManager.get_session(db_settings) as db:
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.agent_id == test_agent_id
            )
            result = await db.execute(stmt)
            db_configs = result.scalars().all()
            assert len(db_configs) == len(test_sync_request_data["configurations"])

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_update_existing(
        self, client, test_agent_id, test_config_data
    ):
        """Test syncing configurations that updates existing configurations."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

            # Create initial configuration
            config = AgentConfiguration(
                id=str(test_config_data["id"]),
                agent_id=test_agent_id,
                name=test_config_data["name"],
                description=test_config_data["description"],
                value=test_config_data["value"],
            )
            db.add(config)
            await db.commit()

        # Update the configuration through sync
        updated_data = {
            "configurations": [
                {
                    "id": str(test_config_data["id"]),
                    "name": "updated_parameter",
                    "description": "Updated description",
                    "value": "updated_value",
                }
            ]
        }

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/agent-configuration", json=updated_data
        )
        assert response.status_code == 200
        configs = response.json()
        assert len(configs) == 1
        assert configs[0]["name"] == "updated_parameter"
        assert configs[0]["value"] == "updated_value"

        # Verify configuration was updated in database
        async with SessionManager.get_session(db_settings) as db:
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.id == str(test_config_data["id"])
            )
            result = await db.execute(stmt)
            updated_config = result.scalar_one()
            assert updated_config.name == "updated_parameter"
            assert updated_config.value == "updated_value"

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_delete_removed(
        self, client, test_agent_id, test_config_data
    ):
        """Test syncing configurations that deletes removed configurations."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

            # Create initial configuration
            config = AgentConfiguration(
                id=str(test_config_data["id"]),
                agent_id=test_agent_id,
                name=test_config_data["name"],
                description=test_config_data["description"],
                value=test_config_data["value"],
            )
            db.add(config)
            await db.commit()

        # Sync with empty configurations (should delete existing)
        empty_data = {"configurations": []}

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/agent-configuration", json=empty_data
        )
        assert response.status_code == 200
        assert response.json() == []

        # Verify configuration was deleted
        async with SessionManager.get_session(db_settings) as db:
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.agent_id == test_agent_id
            )
            result = await db.execute(stmt)
            configs = result.scalars().all()
            assert len(configs) == 0

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_mixed_operations(
        self, client, test_agent_id
    ):
        """Test syncing configurations with mixed create, update, delete operations."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

            # Create initial configurations
            config1_id = uuid.uuid4()
            config2_id = uuid.uuid4()

            config1 = AgentConfiguration(
                id=str(config1_id),
                agent_id=test_agent_id,
                name="old_param1",
                description="Old description 1",
                value="old_value1",
            )
            config2 = AgentConfiguration(
                id=str(config2_id),
                agent_id=test_agent_id,
                name="old_param2",
                description="Old description 2",
                value="old_value2",
            )
            db.add_all([config1, config2])
            await db.commit()

        # Sync: update config1, delete config2, create config3
        config3_id = uuid.uuid4()
        sync_data = {
            "configurations": [
                {
                    "id": str(config1_id),
                    "name": "updated_param1",
                    "description": "Updated description 1",
                    "value": "updated_value1",
                },
                {
                    "id": str(config3_id),
                    "name": "new_param3",
                    "description": "New description 3",
                    "value": "new_value3",
                },
            ]
        }

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/agent-configuration", json=sync_data
        )
        assert response.status_code == 200
        configs = response.json()
        assert len(configs) == 2

        # Verify final state in database
        async with SessionManager.get_session(db_settings) as db:
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.agent_id == test_agent_id
            )
            result = await db.execute(stmt)
            final_configs = result.scalars().all()
            assert len(final_configs) == 2

            # Verify config1 was updated
            config1 = next(c for c in final_configs if c.id == str(config1_id))
            assert config1.name == "updated_param1"
            assert config1.value == "updated_value1"

            # Verify config3 was created
            config3 = next(c for c in final_configs if c.id == str(config3_id))
            assert config3.name == "new_param3"
            assert config3.value == "new_value3"

            # Verify config2 was deleted
            assert not any(c.id == str(config2_id) for c in final_configs)

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_invalid_data(self, client, test_agent_id):
        """Test syncing with invalid data."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

        # Missing required fields
        invalid_data = {
            "configurations": [
                {
                    "id": str(uuid.uuid4()),
                    "name": "param1",
                    # Missing value
                }
            ]
        }

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/agent-configuration", json=invalid_data
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_duplicate_ids(self, client, test_agent_id):
        """Test syncing with duplicate configuration IDs."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

        duplicate_id = str(uuid.uuid4())
        duplicate_data = {
            "configurations": [
                {
                    "id": duplicate_id,
                    "name": "param1",
                    "description": "First instance",
                    "value": "value1",
                },
                {
                    "id": duplicate_id,
                    "name": "param2",
                    "description": "Duplicate ID",
                    "value": "value2",
                },
            ]
        }

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/agent-configuration", json=duplicate_data
        )
        assert response.status_code == 400

    @pytest.mark.asyncio
    async def test_sync_agent_configurations_no_body(self, client, test_agent_id):
        """Test syncing without request body."""
        # Create the agent first
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="A test agent",
            )
            db.add(agent)
            await db.commit()

        response = client.patch(f"/agents/{test_agent_id}/core/v1/agent-configuration")
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_configurations_isolated_by_agent(self, client):
        """Test that configurations are properly isolated by agent ID."""
        agent1_id = "agent-1"
        agent2_id = "agent-2"

        # Create both agents first
        async with SessionManager.get_session(db_settings) as db:
            agent1 = Agent(
                id=agent1_id,
                name="Agent 1",
                url="http://agent1:8000",
                description="First test agent",
            )
            agent2 = Agent(
                id=agent2_id,
                name="Agent 2",
                url="http://agent2:8000",
                description="Second test agent",
            )
            db.add_all([agent1, agent2])
            await db.commit()

            # Create configurations for both agents
            config1_id = uuid.uuid4()
            config2_id = uuid.uuid4()

            config1 = AgentConfiguration(
                id=str(config1_id),
                agent_id=agent1_id,
                name="agent1_param",
                description="Agent 1 parameter",
                value="agent1_value",
            )
            config2 = AgentConfiguration(
                id=str(config2_id),
                agent_id=agent2_id,
                name="agent2_param",
                description="Agent 2 parameter",
                value="agent2_value",
            )
            db.add_all([config1, config2])
            await db.commit()

        # Check that each agent only sees their configurations
        response1 = client.get(f"/agents/{agent1_id}/core/v1/agent-configuration")
        assert response1.status_code == 200
        configs1 = response1.json()
        assert len(configs1) == 1
        assert configs1[0]["name"] == "agent1_param"

        response2 = client.get(f"/agents/{agent2_id}/core/v1/agent-configuration")
        assert response2.status_code == 200
        configs2 = response2.json()
        assert len(configs2) == 1
        assert configs2[0]["name"] == "agent2_param"
