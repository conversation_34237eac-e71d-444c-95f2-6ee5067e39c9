"""Tests for the Agent Manager API."""

from unittest.mock import patch

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from gen_os_sdk_emulator.core.database.session import SessionManager

from gen_os_am_core.api import Agent<PERSON>anager<PERSON><PERSON>
from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.settings import DatabaseSettings

db_settings = DatabaseSettings.get_settings()


class TestAgentManagerAPI:
    """Test cases for the Agent Manager API class."""

    def test_init_default(self):
        """Test default initialization."""
        api = AgentManagerAPI()
        assert api.allow_origins == ["*"]
        assert api.allow_methods == ["*"]
        assert api.allow_headers == ["*"]
        assert api.allow_credentials is True
        assert api.expose_headers == ["*"]

    def test_init_custom(self):
        """Test custom initialization."""
        api = AgentManagerAPI(
            allow_origins=["http://localhost:3000"],
            allow_methods=["GET", "POST"],
            allow_headers=["X-Custom-Header"],
            allow_credentials=False,
            expose_headers=["X-Exposed-Header"],
        )
        assert api.allow_origins == ["http://localhost:3000"]
        assert api.allow_methods == ["GET", "POST"]
        assert api.allow_headers == ["X-Custom-Header"]
        assert api.allow_credentials is False
        assert api.expose_headers == ["X-Exposed-Header"]

    @pytest.mark.asyncio
    async def test_check_database_access(self):
        """Test database access checking."""
        api = AgentManagerAPI()

        # Mock the session manager check
        with patch(
            "gen_os_sdk_emulator.core.database.session.SessionManager.check_connection"
        ) as mock_check:
            mock_check.return_value = True

            # Use the correct import path for services_database_check as used in api.py
            with patch(
                "gen_os_am_core.api.api.services_database_check"
            ) as mock_services_check:
                mock_services_check.return_value = {"am_db": True}

                result = await api._check_database_access()

                assert result["am_db"] is True


class TestAgentManagerAPIIntegration:
    """Integration tests for the Agent Manager API."""

    @pytest_asyncio.fixture
    async def test_agent(self):
        """Create a test agent."""
        test_agent_id = "test-agent-123"
        async with SessionManager.get_session(db_settings) as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent:8000",
                description="Test agent for API tests",
            )
            db.add(agent)
            await db.commit()
            yield test_agent_id

    def test_agent_config_endpoints_mounted(self, test_agent):
        """Test that agent configuration endpoints are properly mounted."""
        api = AgentManagerAPI()
        app = api.create_api()

        with TestClient(app) as client:
            # Test GET endpoint
            response = client.get(f"/agents/{test_agent}/core/v1/agent-configuration")
            assert response.status_code == 200
