"""Basic tests for the workflows package."""

import pytest
from fastapi.testclient import Test<PERSON>lient


def test_database_connection(get_session):
    """Test that database connection works."""
    # This test verifies that the database session can be created
    # The actual connection test would be done in an async test
    assert get_session is not None


@pytest.mark.asyncio
async def test_database_session_creation(get_session):
    """Test that database session can be created and used."""
    async with get_session as session:
        # Just test that we can create a session
        assert session is not None


def test_api_creation(client: TestClient):
    """Test that the API can be created and responds to requests."""
    # Test that the API is properly configured
    # The workflow module doesn't have a health endpoint, so we'll test a different approach
    assert client is not None


def test_workflow_endpoints_available(client: TestClient):
    """Test that workflow endpoints are available."""
    # Test that the router is properly included
    # We'll test with a simple GET request to see if the API responds
    response = client.get("/agents/test-agent/wf/v1/workflows")
    # Should return 200 (empty list) or 422 (validation error for agent_id)
    assert response.status_code in [200, 422]
