# Generate revisions for the correct package

Assumptions:

1. Database is running (`pdm run start-database`)
2. Environment variables set to localhost (`pdm run source-env`)
3. Database is up to date (`pdm run migrate-all` or `alembic upgrade heads`)

Example:

```bash
alembic revision -m "Link agent_id to workflow." --head=wf@head --version-path=../../gen_os_am_workflows/src/alembic/versions --depends-on=72266d413d36 --autogenerate
```

Add the `--depends-on` flag referencing the latest revision where the table/column you want to reference exists.

Depending on the change that you've done, the auto-generation might be empty - in this case you'll need to fill-in the generated script to achieve what you want.

Since we're operating with multiple target metadata at a time it's also possible that the autogenerated scrip contains changes that you don't really care - in that case, simply delete them.

As a general good practice we should keep the database package operable in a standalone setting as well as in the multiple heads scenario (which is how `gen-os-agent-manager` is operating). As an example, for the above bash command I wanted to create a foreign key linking the `Workflow` table with the `Agent` table. This is how the files would look like:

```python
# packages/gen_os_am_workflows/src/gen_os_am_workflows/database/models.py

class Workflow(Base):
    __tablename__ = "workflow"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    name: Mapped[str] = mapped_column(String, nullable=False, unique=True)
    agent_id: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(String, nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.now, onupdate=datetime.now)
    extra_fields: Mapped[list[dict]] = mapped_column(JSON, nullable=False, default=[])
```

> As you can see, there's no reference to the `Agent` table.

```python
#packages/gen_os_am_workflows/src/alembic/versions/f6662d1d5615_link_agent_id_to_workflow.py

"""Link agent_id to workflow.

Revision ID: f6662d1d5615
Revises: 18ea09623354
Create Date: 2025-07-16 15:17:38.798380

"""

from collections.abc import Sequence

import sqlalchemy as sa
from gen_os_am_core.database.models.agents import Agent
from gen_os_sdk_emulator.core.database.migrations import exist_table
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "f6662d1d5615"
down_revision: str | Sequence[str] | None = "18ea09623354"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = "72266d413d36"


def upgrade() -> None:
    """Upgrade schema."""
    # Add foreign key constraint from workflow.agent_id to agents.id
    if exist_table(Agent.__tablename__):
        op.create_foreign_key(
            constraint_name="fk_workflow_agent_id",
            source_table="workflow",
            referent_table="agents",
            local_cols=["agent_id"],
            remote_cols=["id"],
        )


def downgrade() -> None:
    """Downgrade schema."""
    # Remove foreign key constraint
    op.drop_constraint(
        constraint_name="fk_workflow_agent_id",
        table_name="workflow",
        type_="foreignkey",
    )

```

> Check out the `depends_on` as well as the `if` clause checking if the table we want to reference in our foreign key exists.