"""Utility functions for issue-related database operations."""

import logging
import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import String, asc, desc, func, literal, not_, or_, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from gen_os_am_core.api.schemas import (
    IncidentSoftDeleteRequest,
    IssueListFilters,
    IssueUpdateRequest,
    PaginationDetails,
    ReportedIncidentConversationCreate,
    ReportedIncidentConversationUnified,
    ReportedIncidentWorkflowCreate,
    ReportedIncidentWorkflowUnified,
)
from gen_os_am_core.database.models.agent_issue import (
    Issue,
    IssueLog,
    ReportedIncidentConversation,
    ReportedIncidentWorkflow,
)
from gen_os_am_core.models.enums import IssueLogType

logger = logging.getLogger(__name__)


async def get_agent_issues_paginated(
    db: AsyncSession,
    agent_id: str | None,
    filters: IssueListFilters,
) -> tuple[list[dict[str, Any]], PaginationDetails]:
    """Return a list of issues for the given agent using SQL aggregates."""
    # Sub-queries
    conv_cnt_sq = (
        select(
            ReportedIncidentConversation.issue_id.label("issue_id"),
            func.count(ReportedIncidentConversation.id).label("conv_cnt"),
        )
        .group_by(ReportedIncidentConversation.issue_id)
        .subquery()
    )

    wf_cnt_sq = (
        select(
            ReportedIncidentWorkflow.issue_id.label("issue_id"),
            func.count(ReportedIncidentWorkflow.id).label("wf_cnt"),
        )
        .group_by(ReportedIncidentWorkflow.issue_id)
        .subquery()
    )

    conv_severities = select(
        ReportedIncidentConversation.issue_id.label("issue_id"),
        ReportedIncidentConversation.severity.label("severity"),
    )
    wf_severities = select(
        ReportedIncidentWorkflow.issue_id.label("issue_id"),
        ReportedIncidentWorkflow.severity.label("severity"),
    )
    all_severities = conv_severities.union_all(wf_severities).subquery()

    severity_sq = (
        select(
            all_severities.c.issue_id.label("issue_id"),
            func.min(all_severities.c.severity).label("severity"),
        )
        .group_by(all_severities.c.issue_id)
        .subquery()
    )

    # Base statement
    stmt = (
        select(
            Issue.id,
            Issue.description,
            Issue.issue_type,
            Issue.state,
            Issue.created_at,
            Issue.updated_at,
            Issue.agent_id.label("agent_name"),
            severity_sq.c.severity.label("severity"),
            (
                func.coalesce(conv_cnt_sq.c.conv_cnt, 0)
                + func.coalesce(wf_cnt_sq.c.wf_cnt, 0)
            ).label("incidents"),
        )
        .outerjoin(conv_cnt_sq, conv_cnt_sq.c.issue_id == Issue.id)
        .outerjoin(wf_cnt_sq, wf_cnt_sq.c.issue_id == Issue.id)
        .outerjoin(severity_sq, severity_sq.c.issue_id == Issue.id)
    )

    if agent_id is not None:
        stmt = stmt.where(Issue.agent_id == agent_id)

    # Filters
    if filters.is_open:
        stmt = stmt.where(Issue.state == "open")
    else:
        stmt = stmt.where(Issue.state != "open")

    if filters.search:
        search_like = f"%{filters.search}%"
        if filters.search_column:
            target_col = filters.search_column
            if target_col == "id":
                stmt = stmt.where(Issue.id.cast(String).ilike(search_like))
            elif target_col == "description":
                stmt = stmt.where(Issue.description.ilike(search_like))
            elif target_col == "issue_type":
                stmt = stmt.where(Issue.issue_type.ilike(search_like))
        else:
            stmt = stmt.where(
                or_(
                    Issue.id.cast(String).ilike(search_like),
                    Issue.description.ilike(search_like),
                    Issue.issue_type.ilike(search_like),
                )
            )

    # Sorting
    sort_mapping: dict[str, Any] = {
        "created_at": Issue.created_at,
        "updated_at": Issue.updated_at,
        "incidents": func.coalesce(conv_cnt_sq.c.conv_cnt, 0)
        + func.coalesce(wf_cnt_sq.c.wf_cnt, 0),
    }
    sort_expr = sort_mapping.get(filters.sort_field, Issue.created_at)
    order_fn = asc if filters.sort_order == "asc" else desc
    stmt = stmt.order_by(order_fn(sort_expr))

    # Count for pagination
    count_stmt = select(func.count()).select_from(stmt.subquery())

    stmt = stmt.offset(filters.offset).limit(filters.limit)

    records_result = await db.execute(stmt)
    raw_records = records_result.all()
    total_items = (await db.execute(count_stmt)).scalar_one()

    records: list[dict[str, Any]] = [
        {
            "id": row.id,
            "description": row.description,
            "issue_type": row.issue_type,
            "state": row.state,
            "agent_name": row.agent_name,
            "severity": row.severity,
            "incidents": row.incidents,
            "created_at": row.created_at,
            "updated_at": row.updated_at,
        }
        for row in raw_records
    ]

    pagination_details = PaginationDetails(
        offset=filters.offset, limit=filters.limit, total_items=total_items
    )
    return records, pagination_details


async def get_issue_summary(db: AsyncSession, agent_id: str | None) -> dict[str, Any]:
    """Compute summary counts for issues by state and type."""
    if agent_id:
        open_count_stmt = select(func.count()).where(
            Issue.agent_id == agent_id, Issue.state == "open"
        )
        closed_count_stmt = select(func.count()).where(
            Issue.agent_id == agent_id, Issue.state != "open"
        )
        by_type_stmt = select(Issue.issue_type, func.count().label("cnt")).where(
            Issue.agent_id == agent_id
        )
    else:
        open_count_stmt = select(func.count()).where(Issue.state == "open")
        closed_count_stmt = select(func.count()).where(Issue.state != "open")
        by_type_stmt = select(Issue.issue_type, func.count().label("cnt"))

    open_count = (await db.execute(open_count_stmt)).scalar_one()
    closed_count = (await db.execute(closed_count_stmt)).scalar_one()

    by_type_stmt = by_type_stmt.group_by(Issue.issue_type)
    by_type_rows = (await db.execute(by_type_stmt)).all()
    by_type: dict[str, int] = {row.issue_type: row.cnt for row in by_type_rows}

    return {"open": open_count, "closed": closed_count, "by_type": by_type}


async def fetch_issue_record(
    db: AsyncSession, issue_id: uuid.UUID, agent_id: str | None = None
) -> Issue | None:
    """Return the Issue row, optionally checking for agent ownership."""
    stmt = select(Issue).where(Issue.id == issue_id, Issue.deleted.is_(False))
    if agent_id:
        stmt = stmt.where(Issue.agent_id == agent_id)
    return await db.scalar(stmt)


async def fetch_issue_incidents(
    db: AsyncSession,
    issue_id: uuid.UUID,
    incident_type: str = "all",
    incident_sort: str = "desc",
) -> list[ReportedIncidentConversationUnified | ReportedIncidentWorkflowUnified]:
    """Fetch incidents (conversation, workflow, or both) for a given issue."""
    conv_sel = select(
        ReportedIncidentConversation.id.label("id"),
        literal("conversation").label("kind"),
        ReportedIncidentConversation.description,
        ReportedIncidentConversation.severity,
        ReportedIncidentConversation.created_by,
        ReportedIncidentConversation.created_at,
        ReportedIncidentConversation.conversation_id,
        literal(None).label("workflow_execution_id"),
    ).where(
        ReportedIncidentConversation.issue_id == issue_id,
        not_(ReportedIncidentConversation.is_deleted),
    )

    wf_sel = select(
        ReportedIncidentWorkflow.id.label("id"),
        literal("workflow").label("kind"),
        ReportedIncidentWorkflow.description,
        ReportedIncidentWorkflow.severity,
        ReportedIncidentWorkflow.created_by,
        ReportedIncidentWorkflow.created_at,
        literal(None).label("conversation_id"),
        ReportedIncidentWorkflow.workflow_execution_id,
    ).where(
        ReportedIncidentWorkflow.issue_id == issue_id,
        not_(ReportedIncidentWorkflow.is_deleted),
    )

    if incident_type == "conversation":
        incident_stmt = conv_sel
    elif incident_type == "workflow":
        incident_stmt = wf_sel
    else:
        incident_stmt = conv_sel.union_all(wf_sel)

    order_fn = asc if incident_sort == "asc" else desc
    incident_stmt = incident_stmt.order_by(order_fn(text("created_at")))

    rows = (await db.execute(incident_stmt)).all()

    incidents: list[
        ReportedIncidentConversationUnified | ReportedIncidentWorkflowUnified
    ] = []
    for r in rows:
        if r.kind == "conversation":
            incidents.append(
                ReportedIncidentConversationUnified(
                    id=r.id,
                    kind=r.kind,
                    description=r.description,
                    severity=r.severity,
                    created_by=r.created_by,
                    created_at=r.created_at,
                    conversation_id=r.conversation_id,
                )
            )
        else:
            incidents.append(
                ReportedIncidentWorkflowUnified(
                    id=r.id,
                    kind=r.kind,
                    description=r.description,
                    severity=r.severity,
                    created_by=r.created_by,
                    created_at=r.created_at,
                    workflow_execution_id=r.workflow_execution_id,
                )
            )

    return incidents


async def create_issue_with_incidents(
    db: AsyncSession,
    *,
    agent_id: str,
    description: str,
    issue_type: str,
    state: str,
    reported_incidents_conversations: list[ReportedIncidentConversationCreate],
    reported_incidents_workflows: list[ReportedIncidentWorkflowCreate],
    created_by: str | None,
) -> Issue:
    """Create an issue and its related incidents."""
    issue = Issue(
        agent_id=agent_id,
        description=description,
        issue_type=issue_type,
        state=state,
    )

    # Add conversation incidents
    for conv in reported_incidents_conversations:
        incident_conv = ReportedIncidentConversation(
            conversation_id=conv.conversation_id,
            answer=conv.answer,
            description=conv.description,
            severity=conv.severity,
            created_by=created_by,
        )
        issue.reported_incidents_conversations.append(incident_conv)

    # Add workflow incidents
    for wf in reported_incidents_workflows:
        incident_wf = ReportedIncidentWorkflow(
            workflow_execution_id=wf.workflow_execution_id,
            step=wf.step,
            description=wf.description,
            severity=wf.severity,
            created_by=created_by,
        )
        issue.reported_incidents_workflows.append(incident_wf)

    db.add(issue)
    await db.commit()
    await db.refresh(
        issue, ["reported_incidents_conversations", "reported_incidents_workflows"]
    )
    return issue


async def add_incidents_to_issue(
    db: AsyncSession,
    *,
    agent_id: str,
    issue_id: uuid.UUID,
    reported_incidents_conversations: list[ReportedIncidentConversationCreate],
    reported_incidents_workflows: list[ReportedIncidentWorkflowCreate],
    created_by: str | None,
) -> Issue:
    """Add new incidents to an existing issue.

    Args:
        db: Database session
        agent_id: Agent ID to validate ownership
        issue_id: ID of the existing issue
        reported_incidents_conversations: List of conversation incidents to add
        reported_incidents_workflows: List of workflow incidents to add
        created_by: User who is adding the incidents

    Returns:
        Updated issue with new incidents

    Raises:
        ValueError: If issue not found or doesn't belong to agent

    """
    # Fetch and validate the issue exists and belongs to the agent
    issue = await fetch_issue_record(db, issue_id, agent_id)
    if issue is None:
        raise ValueError(f"Issue with ID {issue_id} not found for agent {agent_id}")

    # Add conversation incidents
    for conv in reported_incidents_conversations:
        incident_conv = ReportedIncidentConversation(
            issue_id=issue_id,
            conversation_id=conv.conversation_id,
            answer=conv.answer,
            description=conv.description,
            severity=conv.severity,
            created_by=created_by,
        )
        db.add(incident_conv)

    # Add workflow incidents
    for wf in reported_incidents_workflows:
        incident_wf = ReportedIncidentWorkflow(
            issue_id=issue_id,
            workflow_execution_id=wf.workflow_execution_id,
            step=wf.step,
            description=wf.description,
            severity=wf.severity,
            created_by=created_by,
        )
        db.add(incident_wf)

    await db.commit()
    await db.refresh(
        issue, ["reported_incidents_conversations", "reported_incidents_workflows"]
    )
    return issue


async def soft_delete_restore_incident(
    db: AsyncSession,
    *,
    agent_id: str,
    incident_id: uuid.UUID,
    payload: IncidentSoftDeleteRequest,
) -> tuple[ReportedIncidentConversation | ReportedIncidentWorkflow, str]:
    """Soft delete (or restore) a conversation/workflow incident.

    It validates that the parent issue belongs to the provided agent.

    Returns:
        A tuple containing the updated incident object and its kind.

    Raises:
        ValueError: if the incident is not found or does not belong to the agent.

    """
    # Try conversation incident first
    incident = await db.get(ReportedIncidentConversation, incident_id)
    kind = "conversation"

    if incident is None:
        # If not found, try workflow incident
        incident = await db.get(ReportedIncidentWorkflow, incident_id)
        kind = "workflow"

    if incident is None:
        raise ValueError("Incident not found")

    # Ensure incident belongs to the correct agent via its issue relationship
    await db.refresh(incident, ["issue"])
    if incident.issue.agent_id != agent_id:
        raise ValueError("Incident not found for this agent")

    # Toggle deletion fields
    incident.is_deleted = payload.is_deleted
    if payload.is_deleted:
        incident.deleted_at = datetime.utcnow()
        incident.deleted_by = payload.deleted_by
    else:
        incident.deleted_at = None
        incident.deleted_by = None

    await db.commit()
    await db.refresh(incident)

    return incident, kind


async def update_issue_fields(
    db: AsyncSession,
    *,
    agent_id: str,
    issue_id: uuid.UUID,
    update_data: IssueUpdateRequest,
) -> Issue:
    """Update an existing issue's fields for a specific agent.

    Raises:
        ValueError: if the issue is not found for the given agent.

    """
    issue = await fetch_issue_record(db, issue_id, agent_id)
    if issue is None:
        raise ValueError(f"Issue with ID {issue_id} not found for agent {agent_id}")

    # Update only the provided fields from the request
    update_fields = update_data.model_dump(exclude_unset=True)
    for field, value in update_fields.items():
        setattr(issue, field, value)

    await db.commit()
    await db.refresh(issue)

    return issue


async def get_issue_logs(
    db: AsyncSession,
    *,
    agent_id: str,
    issue_id: uuid.UUID,
    limit: int | None = None,
    offset: int | None = None,
) -> tuple[list[IssueLog], int]:
    """Get issue logs for a specific issue.

    Args:
        db: Database session
        agent_id: Agent ID to validate ownership
        issue_id: ID of the issue to get logs for
        limit: Maximum number of logs to return
        offset: Number of logs to skip

    Returns:
        Tuple of (logs, total_count)

    Raises:
        ValueError: If issue not found or doesn't belong to agent
    """
    # First verify the issue exists and belongs to the agent
    issue_query = select(Issue).where(Issue.id == issue_id, Issue.agent_id == agent_id)
    result = await db.execute(issue_query)
    issue = result.scalar_one_or_none()

    if not issue:
        raise ValueError(f"Issue {issue_id} not found for agent {agent_id}")

    # Get total count
    count_query = select(func.count(IssueLog.id)).where(IssueLog.issue_id == issue_id)
    count_result = await db.execute(count_query)
    total_count = count_result.scalar() or 0

    # Get logs ordered by creation time (newest first)
    logs_query = (
        select(IssueLog)
        .where(IssueLog.issue_id == issue_id)
        .order_by(desc(IssueLog.created_at))
    )

    if limit is not None:
        logs_query = logs_query.limit(limit)
    if offset is not None:
        logs_query = logs_query.offset(offset)

    logs_result = await db.execute(logs_query)
    logs = logs_result.scalars().all()

    return list(logs), total_count


async def create_issue_log(
    db: AsyncSession,
    *,
    issue_id: uuid.UUID,
    log_type: IssueLogType,
    log_metadata: dict | list | None = None,
    created_by: str | None = None,
) -> IssueLog:
    """Create a new issue log entry.

    Args:
        db: Database session
        issue_id: ID of the issue this log belongs to
        log_type: Type of log event
        log_metadata: Optional metadata for the log event
        created_by: User who created this log entry

    Returns:
        Created IssueLog instance
    """
    issue_log = IssueLog(
        issue_id=issue_id,
        log_type=log_type,
        log_metadata=log_metadata,
        created_by=created_by,
    )

    db.add(issue_log)
    await db.commit()
    await db.refresh(issue_log)

    return issue_log


def create_field_change_metadata(
    field_name: str,
    previous_value: Any | None,
    new_value: Any | None,
    comment: str | None = None,
) -> dict:
    """Create metadata for field change logs.

    This creates metadata that matches the frontend LogDetailSchema structure.

    Args:
        field_name: Name of the field that changed
        previous_value: Previous value of the field
        new_value: New value of the field
        comment: Optional comment about the change

    Returns:
        Metadata dict with details array matching frontend schema
    """
    details = [{
        "target": field_name,
        "previousState": str(previous_value) if previous_value is not None else None,
        "newState": str(new_value) if new_value is not None else None,
    }]

    metadata = {"details": details}
    if comment:
        metadata["comment"] = comment

    return metadata


def create_incident_metadata(
    incident_type: str,
    incident_id: str,
    incident_description: str | None = None,
    comment: str | None = None,
) -> dict:
    """Create metadata for incident addition/removal logs.

    Args:
        incident_type: Type of incident ("conversation" or "workflow")
        incident_id: ID of the incident
        incident_description: Optional description of the incident
        comment: Optional comment about the change

    Returns:
        Metadata dict with incident information
    """
    details = [{
        "target": f"{incident_type}_incident",
        "previousState": None,
        "newState": incident_id,
    }]

    metadata = {"details": details}
    if incident_description:
        metadata["incident_description"] = incident_description
    if comment:
        metadata["comment"] = comment

    return metadata


def create_comment_metadata(comment: str) -> dict:
    """Create metadata for comment logs.

    Args:
        comment: The comment text

    Returns:
        Metadata dict with comment
    """
    return {
        "comment": comment,
        "details": []
    }


async def log_issue_field_change(
    db: AsyncSession,
    *,
    issue_id: uuid.UUID,
    field_name: str,
    previous_value: Any | None,
    new_value: Any | None,
    created_by: str | None = None,
    comment: str | None = None,
) -> IssueLog:
    """Log a field change for an issue.

    Args:
        db: Database session
        issue_id: ID of the issue
        field_name: Name of the field that changed
        previous_value: Previous value
        new_value: New value
        created_by: User who made the change
        comment: Optional comment about the change

    Returns:
        Created IssueLog instance
    """
    metadata = create_field_change_metadata(
        field_name=field_name,
        previous_value=previous_value,
        new_value=new_value,
        comment=comment,
    )

    return await create_issue_log(
        db,
        issue_id=issue_id,
        log_type="updated",
        log_metadata=metadata,
        created_by=created_by,
    )


async def log_issue_state_change(
    db: AsyncSession,
    *,
    issue_id: uuid.UUID,
    previous_state: str,
    new_state: str,
    created_by: str | None = None,
    comment: str | None = None,
) -> IssueLog:
    """Log a state change for an issue.

    Args:
        db: Database session
        issue_id: ID of the issue
        previous_state: Previous state
        new_state: New state
        created_by: User who made the change
        comment: Optional comment about the change

    Returns:
        Created IssueLog instance
    """
    # Map state to appropriate log type
    log_type_map = {
        "closed_fixed": "closed_fixed",
        "closed_wont_fix": "closed_wontfix",
        "open": "reopened" if previous_state.startswith("closed") else "updated",
    }

    log_type = log_type_map.get(new_state, "updated")

    metadata = create_field_change_metadata(
        field_name="state",
        previous_value=previous_state,
        new_value=new_state,
        comment=comment,
    )

    return await create_issue_log(
        db,
        issue_id=issue_id,
        log_type=log_type,
        log_metadata=metadata,
        created_by=created_by,
    )


async def log_incident_added(
    db: AsyncSession,
    *,
    issue_id: uuid.UUID,
    incident_type: str,
    incident_id: str,
    incident_description: str | None = None,
    created_by: str | None = None,
) -> IssueLog:
    """Log an incident addition to an issue.

    Args:
        db: Database session
        issue_id: ID of the issue
        incident_type: Type of incident ("conversation" or "workflow")
        incident_id: ID of the incident
        incident_description: Optional description
        created_by: User who added the incident

    Returns:
        Created IssueLog instance
    """
    metadata = create_incident_metadata(
        incident_type=incident_type,
        incident_id=incident_id,
        incident_description=incident_description,
    )

    return await create_issue_log(
        db,
        issue_id=issue_id,
        log_type="added_incident",
        log_metadata=metadata,
        created_by=created_by,
    )


async def log_incident_removed(
    db: AsyncSession,
    *,
    issue_id: uuid.UUID,
    incident_type: str,
    incident_id: str,
    created_by: str | None = None,
) -> IssueLog:
    """Log an incident removal from an issue.

    Args:
        db: Database session
        issue_id: ID of the issue
        incident_type: Type of incident ("conversation" or "workflow")
        incident_id: ID of the incident
        created_by: User who removed the incident

    Returns:
        Created IssueLog instance
    """
    metadata = create_incident_metadata(
        incident_type=incident_type,
        incident_id=incident_id,
    )

    return await create_issue_log(
        db,
        issue_id=issue_id,
        log_type="removed_incident",
        log_metadata=metadata,
        created_by=created_by,
    )


async def create_issue_with_initial_log(
    db: AsyncSession,
    *,
    agent_id: str,
    description: str,
    issue_type: str,
    state: str,
    created_by: str | None = None,
) -> Issue:
    """Create an issue and automatically create an initial 'created' log entry.

    Args:
        db: Database session
        agent_id: Agent ID
        description: Issue description
        issue_type: Issue type/category
        state: Initial state
        created_by: User who created the issue

    Returns:
        Created Issue instance
    """
    # Create the issue
    issue = Issue(
        agent_id=agent_id,
        description=description,
        issue_type=issue_type,
        state=state,
    )

    db.add(issue)
    await db.flush()  # Flush to get the issue ID

    # Create initial log entry
    await create_issue_log(
        db,
        issue_id=issue.id,
        log_type="created",
        log_metadata={
            "comment": f"Issue created with description: {description}",
            "details": [{
                "target": "issue",
                "previousState": None,
                "newState": "created",
            }]
        },
        created_by=created_by,
    )

    await db.commit()
    await db.refresh(issue)

    return issue


async def edit_issue_log(
    db: AsyncSession,
    *,
    agent_id: str,
    issue_id: uuid.UUID,
    log_id: uuid.UUID,
    new_comment: str,
    edited_by: str | None = None,
) -> IssueLog:
    """Edit an issue log entry (currently only supports comments).

    Args:
        db: Database session
        agent_id: Agent ID to validate ownership
        issue_id: ID of the issue
        log_id: ID of the log entry to edit
        new_comment: New comment text
        edited_by: User who is editing the log

    Returns:
        Updated IssueLog instance

    Raises:
        ValueError: If log not found, not a comment, already deleted, or permission denied
    """
    # First verify the issue exists and belongs to the agent
    issue_query = select(Issue).where(Issue.id == issue_id, Issue.agent_id == agent_id)
    result = await db.execute(issue_query)
    issue = result.scalar_one_or_none()

    if not issue:
        raise ValueError(f"Issue {issue_id} not found for agent {agent_id}")

    # Get the log entry
    log_query = select(IssueLog).where(IssueLog.id == log_id, IssueLog.issue_id == issue_id)
    log_result = await db.execute(log_query)
    log_entry = log_result.scalar_one_or_none()

    if not log_entry:
        raise ValueError(f"Log entry {log_id} not found for issue {issue_id}")

    # Validate log can be edited
    if log_entry.log_type != "commented":
        raise ValueError("Only comment logs can be edited")

    if log_entry.is_deleted:
        raise ValueError("Cannot edit a deleted log entry")

    # For now, only allow the author to edit (you can add admin logic later)
    if log_entry.created_by != edited_by:
        raise ValueError("Only the author can edit their comment")

    # Update the log entry
    log_entry.edited_at = func.now()
    log_entry.edited_by = edited_by

    # Update the comment in metadata
    metadata = log_entry.log_metadata or {}
    metadata["comment"] = new_comment
    metadata["was_edited"] = True
    log_entry.log_metadata = metadata

    await db.commit()
    await db.refresh(log_entry)

    return log_entry


async def soft_delete_issue_log(
    db: AsyncSession,
    *,
    agent_id: str,
    issue_id: uuid.UUID,
    log_id: uuid.UUID,
    deleted_by: str | None = None,
) -> IssueLog:
    """Soft delete an issue log entry (currently only supports comments).

    Args:
        db: Database session
        agent_id: Agent ID to validate ownership
        issue_id: ID of the issue
        log_id: ID of the log entry to delete
        deleted_by: User who is deleting the log

    Returns:
        Updated IssueLog instance

    Raises:
        ValueError: If log not found, not a comment, already deleted, or permission denied
    """
    # First verify the issue exists and belongs to the agent
    issue_query = select(Issue).where(Issue.id == issue_id, Issue.agent_id == agent_id)
    result = await db.execute(issue_query)
    issue = result.scalar_one_or_none()

    if not issue:
        raise ValueError(f"Issue {issue_id} not found for agent {agent_id}")

    # Get the log entry
    log_query = select(IssueLog).where(IssueLog.id == log_id, IssueLog.issue_id == issue_id)
    log_result = await db.execute(log_query)
    log_entry = log_result.scalar_one_or_none()

    if not log_entry:
        raise ValueError(f"Log entry {log_id} not found for issue {issue_id}")

    # Validate log can be deleted
    if log_entry.log_type != "commented":
        raise ValueError("Only comment logs can be deleted")

    if log_entry.is_deleted:
        raise ValueError("Log entry is already deleted")

    # For now, only allow the author to delete (you can add admin logic later)
    if log_entry.created_by != deleted_by:
        raise ValueError("Only the author can delete their comment")

    # Soft delete the log entry
    log_entry.is_deleted = True
    log_entry.deleted_at = func.now()
    log_entry.deleted_by = deleted_by

    # Optionally clear the comment from metadata for privacy
    metadata = log_entry.log_metadata or {}
    metadata["comment"] = None  # Clear the comment text
    log_entry.log_metadata = metadata

    await db.commit()
    await db.refresh(log_entry)

    return log_entry


async def get_issues_for_conversation(
    db: AsyncSession,
    *,
    conversation_id: str,
    agent_id: str | None = None,
) -> list[dict[str, Any]]:
    """Get all issues related to a specific conversation.

    Args:
        db: Database session
        conversation_id: ID of the conversation
        agent_id: Optional agent ID to filter by

    Returns:
        List of issue dictionaries with basic information
    """
    # Build query to find issues with conversation incidents
    stmt = (
        select(
            Issue.id,
            Issue.description,
            Issue.issue_type,
            Issue.state,
            Issue.created_at,
            Issue.updated_at,
            ReportedIncidentConversation.severity,
        )
        .join(ReportedIncidentConversation, Issue.id == ReportedIncidentConversation.issue_id)
        .where(
            ReportedIncidentConversation.conversation_id == conversation_id,
            not_(ReportedIncidentConversation.is_deleted),
            Issue.deleted.is_(False),
        )
    )

    if agent_id:
        stmt = stmt.where(Issue.agent_id == agent_id)

    result = await db.execute(stmt)
    rows = result.all()

    # Convert to list of dictionaries
    issues = []
    for row in rows:
        issues.append({
            "id": str(row.id),
            "description": row.description,
            "issue_type": row.issue_type,
            "state": row.state,
            "severity": row.severity,
            "created_at": row.created_at.isoformat(),
            "updated_at": row.updated_at.isoformat(),
        })

    return issues


async def get_issues_for_workflow_execution(
    db: AsyncSession,
    *,
    workflow_execution_id: str,
    agent_id: str | None = None,
) -> list[dict[str, Any]]:
    """Get all issues related to a specific workflow execution.

    Args:
        db: Database session
        workflow_execution_id: ID of the workflow execution
        agent_id: Optional agent ID to filter by

    Returns:
        List of issue dictionaries with basic information
    """
    # Build query to find issues with workflow incidents for this specific execution
    stmt = (
        select(
            Issue.id,
            Issue.description,
            Issue.issue_type,
            Issue.state,
            Issue.created_at,
            Issue.updated_at,
            ReportedIncidentWorkflow.severity,
            ReportedIncidentWorkflow.workflow_execution_id,
            ReportedIncidentWorkflow.step,
        )
        .join(ReportedIncidentWorkflow, Issue.id == ReportedIncidentWorkflow.issue_id)
        .where(
            ReportedIncidentWorkflow.workflow_execution_id == workflow_execution_id,
            not_(ReportedIncidentWorkflow.is_deleted),
            Issue.deleted.is_(False),
        )
    )

    if agent_id:
        stmt = stmt.where(Issue.agent_id == agent_id)

    result = await db.execute(stmt)
    rows = result.all()

    # Convert to list of dictionaries
    issues = []
    for row in rows:
        issues.append({
            "id": str(row.id),
            "description": row.description,
            "issue_type": row.issue_type,
            "state": row.state,
            "severity": row.severity,
            "workflow_execution_id": row.workflow_execution_id,
            "step": row.step,
            "created_at": row.created_at.isoformat(),
            "updated_at": row.updated_at.isoformat(),
        })

    return issues


async def get_issues_for_workflow(
    db: AsyncSession,
    *,
    workflow_name: str,
    agent_id: str | None = None,
) -> list[dict[str, Any]]:
    """Get all issues related to a specific workflow.

    Args:
        db: Database session
        workflow_name: Name of the workflow
        agent_id: Optional agent ID to filter by

    Returns:
        List of issue dictionaries with basic information
    """
    # Build query to find issues with workflow incidents
    # We'll match by workflow_execution_id pattern or step information
    stmt = (
        select(
            Issue.id,
            Issue.description,
            Issue.issue_type,
            Issue.state,
            Issue.created_at,
            Issue.updated_at,
            ReportedIncidentWorkflow.severity,
            ReportedIncidentWorkflow.workflow_execution_id,
            ReportedIncidentWorkflow.step,
        )
        .join(ReportedIncidentWorkflow, Issue.id == ReportedIncidentWorkflow.issue_id)
        .where(
            not_(ReportedIncidentWorkflow.is_deleted),
            Issue.deleted.is_(False),
        )
    )

    if agent_id:
        stmt = stmt.where(Issue.agent_id == agent_id)

    result = await db.execute(stmt)
    rows = result.all()

    # Filter by workflow name (since workflow_execution_id might contain workflow name)
    # and convert to list of dictionaries
    issues = []
    for row in rows:
        # Simple heuristic: check if workflow name is in the execution ID
        # This might need refinement based on your workflow execution ID format
        if workflow_name.lower() in (row.workflow_execution_id or "").lower():
            issues.append({
                "id": str(row.id),
                "description": row.description,
                "issue_type": row.issue_type,
                "state": row.state,
                "severity": row.severity,
                "workflow_execution_id": row.workflow_execution_id,
                "step": row.step,
                "created_at": row.created_at.isoformat(),
                "updated_at": row.updated_at.isoformat(),
            })

    return issues
