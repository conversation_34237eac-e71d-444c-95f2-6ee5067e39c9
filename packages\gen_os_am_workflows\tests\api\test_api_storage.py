"""Unit tests for the API storage integration."""

import uuid
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mock, patch

import pytest
from fastapi.testclient import TestClient
from gen_os_sdk_emulator.infrastructure.storage import (
    DownloadInfo,
    FileMetadata,
    StorageInterface,
    UploadInfo,
)

from gen_os_am_workflows.api.api import API

app = API().create_api()

# Test agent ID to use in all tests
TEST_AGENT_ID = "test-agent-123"

ENDPOINT_PREFIX = "/agents/{agent_id}/wf/v1"


@pytest.fixture
def mock_db_session():
    """Mock the database session."""
    db_session = AsyncMock()

    # Configure the mock to work as a context manager
    db_session.__aenter__.return_value = db_session
    db_session.__aexit__.return_value = None

    # Add common mock behaviors
    db_session.add = MagicMock()
    db_session.commit = AsyncMock()
    db_session.refresh = AsyncMock()

    return db_session


@pytest.fixture
def api_client(mock_db_session):
    """Create a test client for the API with mocked dependencies."""
    with patch(
        "gen_os_sdk_emulator.core.database.session.SessionManager.get_session",
        return_value=mock_db_session,
    ):
        client = TestClient(app)
        yield client, mock_db_session


class TestAPIStorageIntegration:
    """Tests for the API's integration with the storage service."""

    @pytest.mark.asyncio
    async def test_create_file_endpoint(self, api_client):
        """Test that the create file endpoint correctly uses the storage service."""
        # Arrange
        client, mock_db_session = api_client

        # Set up the DB session to add, commit and refresh a File model
        mock_file = MagicMock()
        mock_file.id = uuid.UUID("12345678-1234-5678-1234-************")
        mock_file.file_name = "test_file.txt"
        mock_file.storage_path = ""  # Initial empty path

        # Override the generic side effect with a test-specific one
        mock_db_session.refresh.side_effect = lambda x: setattr(x, "id", mock_file.id)

        storage_service_mock = Mock(spec=StorageInterface)
        storage_service_mock.get_storage_path.return_value = f"files/{mock_file.id}.txt"
        storage_service_mock.generate_upload_url.return_value = UploadInfo(
            file_id=mock_file.id, upload_url="https://example.com/upload"
        )
        storage_service_mock.generate_download_url.return_value = DownloadInfo(
            file_id=mock_file.id,
            download_url="https://example.com/download",
            file_name="test_file.txt",
        )

        # Act
        with app.container.storage_service.override(storage_service_mock):
            with patch("gen_os_am_workflows.api.router.File") as mock_file_model:
                mock_file_model.return_value = mock_file
                response = client.post(
                    f"{ENDPOINT_PREFIX}/files",
                    json={"file_name": "test_file.txt", "content_type": "text/plain"},
                )

        # Assert
        assert response.status_code == 200
        assert response.json() == {
            "file_id": "12345678-1234-5678-1234-************",
            "upload_url": "https://example.com/upload",
        }

        # Verify the database operations
        mock_db_session.add.assert_called_once()
        assert mock_db_session.commit.call_count == 2
        assert (
            mock_db_session.refresh.call_count == 2
        )  # Once for initial creation, once for storage path update

        # Verify the storage service was called correctly
        storage_service_mock.get_storage_path.assert_called_once_with(
            file_id=mock_file.id, file_name=mock_file.file_name
        )

        # Verify the FileMetadata passed to generate_upload_url
        call_args = storage_service_mock.generate_upload_url.call_args[0][0]
        assert isinstance(call_args, FileMetadata)
        assert call_args.id == mock_file.id
        assert call_args.file_name == "test_file.txt"
        # The storage path should match our implementation format
        expected_path = f"files/{mock_file.id}.txt"
        assert call_args.storage_path == expected_path
        assert call_args.content_type == "text/plain"

    @pytest.mark.asyncio
    async def test_get_file_endpoint(self, api_client):
        """Test that the get file endpoint correctly uses the storage service."""
        # Arrange
        client, mock_db_session = api_client
        file_id = "12345678-1234-5678-1234-************"

        # Set up the DB session to return a File model
        mock_file = MagicMock()
        mock_file.id = uuid.UUID(file_id)
        mock_file.file_name = "test_file.txt"
        mock_file.storage_path = f"files/{file_id}.txt"

        mock_db_session.scalar = AsyncMock(return_value=mock_file)

        # Create a mock storage service
        storage_service_mock = Mock(spec=StorageInterface)
        storage_service_mock.generate_download_url.return_value = DownloadInfo(
            file_id=mock_file.id,
            download_url="https://example.com/download",
            file_name="test_file.txt",
        )

        # Act
        with app.container.storage_service.override(storage_service_mock):
            response = client.get(f"{ENDPOINT_PREFIX}/files/{file_id}")

        # Assert
        assert response.status_code == 200
        assert response.json() == {
            "id": file_id,
            "url": "https://example.com/download",
            "file_name": "test_file.txt",
        }

        # Verify the storage service was used correctly
        storage_service_mock.generate_download_url.assert_called_once()

        # Verify the FileMetadata passed to generate_download_url
        call_args = storage_service_mock.generate_download_url.call_args[0][0]
        assert isinstance(call_args, FileMetadata)
        assert call_args.id == mock_file.id
        assert call_args.file_name == "test_file.txt"
        assert call_args.storage_path == f"files/{file_id}.txt"
