"""General config and settings for Agent Manager."""

from typing import Literal

import dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

dot_env = dotenv.find_dotenv(usecwd=True)  # type: ignore

AGENT_MANAGER_PREFIX = "AM_CORE_"


class BaseAgentManagerSettings(BaseSettings):
    """Base settings for the Agent Manager module."""

    model_config = SettingsConfigDict(
        case_sensitive=True,  # don't ignore case in .env/env vars
        env_file=dot_env,  # search current and parent
        frozen=True,  # don't allow changes
        extra="allow",  # allow extra configs
        str_strip_whitespace=True,
        env_prefix=AGENT_MANAGER_PREFIX,
    )

    @classmethod
    def get_settings(cls):
        """Return the settings read from .env or environment."""
        return cls()


class Settings(BaseAgentManagerSettings):
    """Combined settings for the Agent Manager application.

    This class combines all settings for backward compatibility and convenience.
    """

    # API Settings
    LOG_LEVEL: Literal["DEBUG", "INFO", "WARNING", "ERROR"] = "INFO"
    API_LISTEN_HOST: str = "0.0.0.0"
    API_LISTEN_PORT: int = 8080
    API_WORKERS: int = 2
    API_TIMEOUT_KEEP_ALIVE: int = 5
    API_RELOAD: bool = False

    # Workflow Orchestration Settings
    WORKFLOW_AGENT_RETRY_COUNT: int = 3
    WORKFLOW_AGENT_TIMEOUT: int = 10
    WORKFLOW_STATE_STORE: Literal["database", "inmemory"] = "database"


class DatabaseSettings(BaseSettings):
    """The settings for the Agent Manager database."""

    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=dot_env,
        frozen=True,
        extra="allow",
        str_strip_whitespace=True,
    )

    DATABASE_TYPE: Literal["sqlite", "postgresql", "mysql", "sqlserver", "parquet"] = (
        "postgresql"
    )
    DATABASE_NAME: str
    DATABASE_USER: str
    DATABASE_PORT: int
    DATABASE_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_POOL_SIZE: int = 20
    INSTANCE_UNIX_SOCKET: str | None = None

    @classmethod
    def get_settings(cls):
        """Return the settings read from .env or environment."""
        return cls()
