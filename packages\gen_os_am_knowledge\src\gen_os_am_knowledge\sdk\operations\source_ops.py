"""Module for source operations."""

import logging
import uuid
from datetime import datetime, timezone
from typing import Any

from gen_os_sdk_emulator.core.database.session import SessionManager
from sqlmodel import delete, select

from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.config.settings import DatabaseSettings
from gen_os_am_knowledge.sdk.models import Source
from gen_os_am_knowledge.sdk.operations.utils import (
    NOT_PROVIDED,
    DeletionResult,
    UpdateResult,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
    start_time_var,
)

db_settings = DatabaseSettings.get_settings()


class SourceService:
    """Service class to handle CRUD operations for source model."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the SourceService with an optional logger."""
        self.logger = logger or get_sdk_logger()

    @log_decorator()
    async def create_source(
        self, source_name: str, source_type: str, meta: dict[Any, Any] | None = None
    ) -> Source:
        """Create a new source.

        Args:
            source_name: The name of the source.
            source_type: The type of the source.
            meta: The metadata of the source.

        Returns:
            Source: The created source instance.

        """
        async with SessionManager.get_session(db_settings) as session:
            source = Source(name=source_name, type=source_type, meta=meta)

            session.add(source)
            await session.commit()
            await session.refresh(source)
            return source

    @log_decorator()
    async def delete_source(self, source_id: uuid.UUID) -> DeletionResult:
        """Delete source and return the result of the operation.

        Args:
            source_id: The id of the source to delete.

        Returns:
            DeletionResult: The result of the deletion operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = delete(Source).where(Source.id == source_id)
            deleted_source = (
                await session.execute(query.returning(Source))
            ).scalar_one_or_none()
            if not deleted_source:
                error_event_log(
                    f"Source with id {source_id} not found.",
                    start,
                    Exception(f"Source with id {source_id} not found."),
                    self.logger,
                )
                return DeletionResult(
                    success=False, error=f"Source with id {source_id} not found."
                )
            else:
                deleted_source = deleted_source.model_copy()
                await session.commit()
                return DeletionResult(success=True, deleted_instance=deleted_source)

    @log_decorator(type_="access")
    async def get_source(self, source_id: uuid.UUID) -> Source | None:
        """Retrieve source from the database based on source id.

        Args:
            source_id: The source id to search for.

        Returns:
            Source: The source instance if found, None otherwise.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Source).where(Source.id == source_id)
            return (await session.execute(query)).scalars().first()

    @log_decorator(type_="access")
    async def search_sources(
        self,
        source_name: str | None = None,
        source_type: str | None = None,
        metadata_field: dict | None = None,
        limit: int = 20,
        offset: int = 0,
    ) -> list[Source]:
        """Retrieve sources from the database.

        If None of the attributes is given, retrieves all the sources.

        Args:
            source_name: Source name to search for.
            source_type: Source type to search for.
            metadata_field: Metadata field to search for.
            limit: The maximum number of sources to return.
            offset: The number of sources to skip.

        Returns:
            list[Source]: The list of sources that match the search criteria.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(Source)
            if source_name:
                query = query.where(Source.name == source_name)
            if source_type:
                query = query.where(Source.type == source_type)
            if metadata_field:
                for key, value in metadata_field.items():
                    query = query.where(Source.meta.op("->>")(key) == value)

            query = query.limit(limit).offset(offset)
            return (await session.execute(query)).scalars().all()

    @log_decorator()
    async def update_source(
        self,
        source_id: uuid.UUID,
        source_name: str | type[NOT_PROVIDED] = NOT_PROVIDED,
        source_type: str | type[NOT_PROVIDED] = NOT_PROVIDED,
        meta: dict[Any, Any] | None | type[NOT_PROVIDED] = NOT_PROVIDED,
    ) -> UpdateResult:
        """Update a source in the database.

        Args:
            source_id: The id of the source to update. Only required argument.
            source_name: The new name of the source.
            source_type: The new type of the source.
            meta: The new metadata of the source.

        Returns:
            UpdateResult: The result of the update operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = select(Source).where(Source.id == source_id)
            source = (await session.execute(query)).scalar_one_or_none()
            if not source:
                error_event_log(
                    f"Source with id {source_id} not found.",
                    start,
                    Exception(f"Source with id {source_id} not found."),
                    self.logger,
                )
                return UpdateResult(
                    success=False, error=f"Source with id {source_id} not found."
                )
            else:
                update_dict = {
                    "name": source_name,
                    "type": source_type,
                    "meta": meta,
                }
                updated_values = {}
                for k, v in update_dict.items():
                    if v is not NOT_PROVIDED:
                        if getattr(source, k) != v:
                            updated_values[k] = (getattr(source, k), v)
                            setattr(source, k, v)
                session.add(source)
                source = source.model_copy()
                await session.commit()
                return UpdateResult(
                    success=True, updated_instance=source, updated_values=updated_values
                )
