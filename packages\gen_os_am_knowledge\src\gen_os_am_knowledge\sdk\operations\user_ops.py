"""Module for user operations."""

import logging
import uuid
from datetime import datetime, timezone

from gen_os_sdk_emulator.core.database.session import SessionManager
from sqlmodel import delete, select

from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.config.settings import DatabaseSettings
from gen_os_am_knowledge.sdk.models import User
from gen_os_am_knowledge.sdk.operations.utils import (
    NOT_PROVIDED,
    DeletionResult,
    UpdateResult,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
    start_time_var,
)

db_settings = DatabaseSettings.get_settings()


class UserService:
    """Service class to handle CRUD operations for user model."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the UserService with an optional logger."""
        self.logger = logger or get_sdk_logger()

    @log_decorator()
    async def create_user(self, user_name: str, user_external_id: str) -> User:
        """Create a new user.

        Args:
            user_name: string that defines the user name
            user_external_id: User id from external service

        Returns:
            The created user instance.

        """
        async with SessionManager.get_session(db_settings) as session:
            user = User(name=user_name, external_id=user_external_id)
            session.add(user)
            await session.commit()
            await session.refresh(user)
            return user

    @log_decorator()
    async def delete_user(self, user_id: uuid.UUID) -> DeletionResult:
        """Delete user based on user id.

        Args:
            user_id: The id of the user to delete.

        Returns:
            DeletionResult: The result of the deletion operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = delete(User).where(User.id == user_id)
            deleted_user = (
                await session.execute(query.returning(User))
            ).scalar_one_or_none()
            if not deleted_user:
                error_event_log(
                    msg=f"User with id {user_id} not found.",
                    start=start,
                    error=Exception(f"User with id {user_id} not found."),
                    logger=self.logger,
                )
                return DeletionResult(
                    success=False, error=f"User with id {user_id} not found."
                )
            else:
                deleted_user = deleted_user.model_copy()
                await session.commit()
                return DeletionResult(success=True, deleted_instance=deleted_user)

    @log_decorator(type_="access")
    async def get_user(self, user_id: uuid.UUID) -> User:
        """Retrieve user from the database.

        Args:
            user_id: The id of the user to retrieve.

        Returns:
            User: The user instance retrieved.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(User).where(User.id == user_id)
            user = (await session.execute(query)).scalars().first()
            return user

    @log_decorator(type_="access")
    async def search_users(
        self,
        user_name: str | None = None,
        user_external_id: str | None = None,
        limit: int = 20,
        offset: int = 0,
    ) -> list[User]:
        """Search for users based on user name or external id.

        Args:
            user_name: The name of the user to search for.
            user_external_id: The external id of the user to search for.
            limit: The number of results to return.
            offset: The number of results to skip.

        Returns:
            list[User]: A list of user instances.

        """
        async with SessionManager.get_session(db_settings) as session:
            query = select(User)

            if user_name:
                query = query.where(User.name == user_name)
            if user_external_id:
                query = query.where(User.external_id == user_external_id)

            query = query.limit(limit).offset(offset)
            return (await session.execute(query)).scalars().all()

    @log_decorator()
    async def update_user(
        self,
        user_id: uuid.UUID,
        user_name: str | type[NOT_PROVIDED] = NOT_PROVIDED,
        user_external_id: str | type[NOT_PROVIDED] = NOT_PROVIDED,
    ) -> UpdateResult:
        """Update user based on user id.

        Args:
            user_id: The id of the user to update.
            user_name: The new name of the user.
            user_external_id: The new external id of the user.

        Returns:
            UpdateResult: The result of the update operation

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async with SessionManager.get_session(db_settings) as session:
            query = select(User).where(User.id == user_id)
            user = (await session.execute(query)).scalars().one_or_none()
            if not user:
                error_event_log(
                    msg=f"User with id {user_id} not found.",
                    start=start,
                    error=Exception(f"User with id {user_id} not found."),
                    logger=self.logger,
                )
                return UpdateResult(
                    success=False, error=f"User with id {user_id} not found."
                )
            else:
                update_dict = {"name": user_name, "external_id": user_external_id}
                updated_values = {}
                for k, v in update_dict.items():
                    if v is not NOT_PROVIDED:
                        if getattr(user, k) != v:
                            updated_values[k] = (getattr(user, k), v)
                            setattr(user, k, v)
                session.add(user)
                user = user.model_copy()
                await session.commit()
                return UpdateResult(
                    success=True, updated_instance=user, updated_values=updated_values
                )
