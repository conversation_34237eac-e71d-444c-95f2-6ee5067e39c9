"""Add delete on cascade to Workflow and WorkflowExecution.

Revision ID: 21d7eb371a87
Revises: edaf98afb6a3
Create Date: 2025-07-16 18:07:26.474948

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "21d7eb371a87"
down_revision: str | Sequence[str] | None = "edaf98afb6a3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    # Drop existing foreign key constraints
    op.drop_constraint(
        "workflow_execution_workflow_id_fkey", "workflow_execution", type_="foreignkey"
    )
    op.drop_constraint("step_run_workflow_execution_id_fkey", "step_run", type_="foreignkey")
    op.drop_constraint("step_input_step_id_fkey", "step_input", type_="foreignkey")
    op.drop_constraint("step_output_step_id_fkey", "step_output", type_="foreignkey")
    op.drop_constraint("step_run_step_id_fkey", "step_run", type_="foreignkey")
    op.drop_constraint(
        "workflow_execution_history_workflow_execution_id_fkey",
        "workflow_execution_history",
        type_="foreignkey",
    )

    # Re-create foreign key constraints with ON DELETE CASCADE
    op.create_foreign_key(
        "workflow_execution_workflow_id_fkey",
        "workflow_execution",
        "workflow",
        ["workflow_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "step_run_workflow_execution_id_fkey",
        "step_run",
        "workflow_execution",
        ["workflow_execution_id"],
        ["id"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "step_input_step_id_fkey", "step_input", "step", ["step_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        "step_output_step_id_fkey", "step_output", "step", ["step_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        "step_run_step_id_fkey", "step_run", "step", ["step_id"], ["id"], ondelete="CASCADE"
    )
    op.create_foreign_key(
        "workflow_execution_history_workflow_execution_id_fkey",
        "workflow_execution_history",
        "workflow_execution",
        ["workflow_execution_id"],
        ["id"],
        ondelete="CASCADE",
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop cascade delete constraints
    op.drop_constraint(
        "workflow_execution_workflow_id_fkey", "workflow_execution", type_="foreignkey"
    )
    op.drop_constraint("step_run_workflow_execution_id_fkey", "step_run", type_="foreignkey")
    op.drop_constraint("step_input_step_id_fkey", "step_input", type_="foreignkey")
    op.drop_constraint("step_output_step_id_fkey", "step_output", type_="foreignkey")
    op.drop_constraint("step_run_step_id_fkey", "step_run", type_="foreignkey")
    op.drop_constraint(
        "workflow_execution_history_workflow_execution_id_fkey",
        "workflow_execution_history",
        type_="foreignkey",
    )

    # Re-create foreign key constraints without ON DELETE CASCADE
    op.create_foreign_key(
        "workflow_execution_workflow_id_fkey",
        "workflow_execution",
        "workflow",
        ["workflow_id"],
        ["id"],
    )
    op.create_foreign_key(
        "step_run_workflow_execution_id_fkey",
        "step_run",
        "workflow_execution",
        ["workflow_execution_id"],
        ["id"],
    )
    op.create_foreign_key("step_input_step_id_fkey", "step_input", "step", ["step_id"], ["id"])
    op.create_foreign_key("step_output_step_id_fkey", "step_output", "step", ["step_id"], ["id"])
    op.create_foreign_key("step_run_step_id_fkey", "step_run", "step", ["step_id"], ["id"])
    op.create_foreign_key(
        "workflow_execution_history_workflow_execution_id_fkey",
        "workflow_execution_history",
        "workflow_execution",
        ["workflow_execution_id"],
        ["id"],
    )
