"""Make agent_id unique in the workflows table.

Revision ID: edaf98afb6a3
Revises: f6662d1d5615
Create Date: 2025-07-16 15:35:41.777402

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "edaf98afb6a3"
down_revision: str | Sequence[str] | None = "f6662d1d5615"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = "72266d413d36"


def upgrade() -> None:
    """Upgrade schema."""
    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.create_unique_constraint(None, ["agent_id"])


def downgrade() -> None:
    """Downgrade schema."""
    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.drop_constraint(None, type_="unique")
