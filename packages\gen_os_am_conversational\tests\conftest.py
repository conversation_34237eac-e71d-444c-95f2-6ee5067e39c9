"""Test configuration and fixtures for the conversational API tests.

This module provides pytest fixtures for testing the conversational API,
including database setup, test client configuration, and environment
variable management for the test environment.
"""

import logging
import os
import sys
from pathlib import Path

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient

# Add the src directory to the Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set environment variables BEFORE any imports to ensure they're available
database_file = f"{Path(__file__).parent}/test_database.db"

os.environ["DATABASE_NAME"] = database_file
os.environ["DATABASE_TYPE"] = "sqlite"
os.environ["DATABASE_USER"] = ""
os.environ["DATABASE_PORT"] = "5432"
os.environ["DATABASE_PASSWORD"] = ""
os.environ["DATABASE_HOST"] = ""
os.environ["AM_CONV_API_LISTEN_HOST"] = "localhost"
os.environ["AM_CONV_API_LISTEN_PORT"] = "8081"
os.environ["AM_CONV_API_WORKERS"] = "1"
os.environ["AM_CONV_LOG_LEVEL"] = "DEBUG"

from gen_os_sdk_emulator.core.database.session import SessionManager  # noqa: E402

from gen_os_am_conversational.models.models import Base  # noqa: E402
from gen_os_am_conversational.settings import DatabaseSettings  # noqa: E402
from main import app  # noqa: E402

db_settings = DatabaseSettings.get_settings()


@pytest.fixture(scope="module")
def client(create_tables, config):
    """Test client fixture with database tables created."""
    logger = logging.getLogger("api")
    logger.level = logging.DEBUG

    with TestClient(app) as client:
        yield client


@pytest_asyncio.fixture(scope="session")
async def config():
    """Configure test environment variables."""
    # Environment variables are already set at module level
    pass


@pytest_asyncio.fixture(scope="session")
async def create_tables(config):
    """Create database tables for testing."""
    async with SessionManager.get_session(db_settings) as db_session:
        await (await db_session.connection()).run_sync(Base.metadata.create_all)
        yield
        if Path(database_file).exists():
            os.remove(database_file)


@pytest_asyncio.fixture(scope="session")
async def get_session(create_tables):
    """Get database session for testing."""
    return SessionManager.get_session(db_settings)
