"""FastAPI router for agent configuration management endpoints."""

import logging
from collections.abc import Callable
from functools import partial

from fastapi import APIRouter, Depends, HTTPException, Path, Request
from gen_os_sdk_emulator.core.database.session import get_session
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from gen_os_am_core.api.schemas import (
    AgentConfigurationResponse,
    AgentConfigurationSyncRequest,
)
from gen_os_am_core.database.models.agent_configuration import AgentConfiguration
from gen_os_am_core.settings import DatabaseSettings

db_session: Callable = partial(get_session, DatabaseSettings.get_settings())


class AgentConfigRouter:
    """Agent configuration router for the Agent Manager API."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the agent configuration router.

        Args:
            logger: Optional logger instance

        """
        self.logger = logger or logging.getLogger(__name__)
        self.router = APIRouter(
            prefix="/agents/{agent_id}/core/v1",
            dependencies=[Depends(self._agent_id_dependency)],
            tags=["Agent Configuration"],
        )
        self._setup_routes()

    @staticmethod
    def _agent_id_dependency(agent_id: str = Path(...)):
        """Extract agent_id from the path.

        This allows us to add agent_id to the router prefix without
        adding it to every endpoint signature.
        """
        return agent_id

    def _setup_routes(self):
        """Set up the agent configuration routes."""

        @self.router.get(
            "/agent-configuration",
            response_model=list[AgentConfigurationResponse],
            summary="List all configuration parameters for an agent",
            description="Retrieve all configuration parameters for a specific agent.",
        )
        async def list_agent_configurations(
            request: Request,
            db: AsyncSession = Depends(db_session),
        ):
            """Get all configuration parameters for a specific agent."""
            agent_id = request.path_params["agent_id"]
            try:
                stmt = select(AgentConfiguration).where(
                    AgentConfiguration.agent_id == agent_id
                )
                result = await db.execute(stmt)
                configurations = result.scalars().all()

                return [
                    AgentConfigurationResponse.model_validate(config)
                    for config in configurations
                ]

            except Exception as e:
                self.logger.error(
                    f"Unexpected error in list_agent_configurations: {e}",
                    exc_info=True,
                )
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

        @self.router.patch(
            "/agent-configuration",
            response_model=list[AgentConfigurationResponse],
            summary="Synchronize agent configuration parameters",
            description="""Synchronize configuration parameters for a specific agent.
                        Creates, updates, and deletes configurations as needed.""",
        )
        async def sync_agent_configurations(
            request: Request,
            sync_request: AgentConfigurationSyncRequest,
            db: AsyncSession = Depends(db_session),
        ):
            """Synchronize configuration parameters for an agent.

            This endpoint receives a list of configurations and
            synchronizes the database state:
            - Creates new configurations that don't exist
            - Updates existing configurations with changed values
            - Deletes configurations that are no longer provided
            """
            agent_id = request.path_params["agent_id"]

            # Validate for duplicate IDs in the request
            provided_ids = [config.id for config in sync_request.configurations]
            if len(provided_ids) != len(set(provided_ids)):
                raise HTTPException(
                    status_code=400,
                    detail="Duplicate configuration IDs found in the request",
                )

            try:
                # Get current configurations from database
                stmt = select(AgentConfiguration).where(
                    AgentConfiguration.agent_id == agent_id
                )
                result = await db.execute(stmt)
                current_configs = result.scalars().all()
                current_configs_dict = {config.id: config for config in current_configs}

                # Create dictionaries for easier comparison
                new_configs_dict = {
                    config.id: config for config in sync_request.configurations
                }

                # Identify what operations need to be performed
                current_ids = set(current_configs_dict.keys())
                new_ids = set(new_configs_dict.keys())

                to_create = new_ids - current_ids
                to_delete = current_ids - new_ids
                to_potentially_update = current_ids & new_ids

                # Create new configurations
                for config_id in to_create:
                    config_data = new_configs_dict[config_id]
                    new_config = AgentConfiguration(
                        id=config_data.id,
                        agent_id=agent_id,
                        name=config_data.name,
                        description=config_data.description,
                        value=config_data.value,
                    )
                    db.add(new_config)
                    self.logger.info(
                        f"Creating new configuration {config_id} for agent {agent_id}"
                    )

                # Delete removed configurations
                for config_id in to_delete:
                    config = current_configs_dict[config_id]
                    await db.delete(config)
                    self.logger.info(
                        f"Deleting configuration {config_id} for agent {agent_id}"
                    )

                # Update existing configurations if they have changed
                for config_id in to_potentially_update:
                    current_config = current_configs_dict[config_id]
                    new_config_data = new_configs_dict[config_id]

                    # Check if any fields have changed
                    needs_update = (
                        current_config.name != new_config_data.name
                        or current_config.description != new_config_data.description
                        or current_config.value != new_config_data.value
                    )

                    if needs_update:
                        current_config.name = new_config_data.name
                        current_config.description = new_config_data.description
                        current_config.value = new_config_data.value
                        self.logger.info(
                            f"Updating configuration {config_id} for agent {agent_id}"
                        )

                # Commit all changes
                await db.commit()

                # Get the final state of all configurations
                stmt = select(AgentConfiguration).where(
                    AgentConfiguration.agent_id == agent_id
                )
                result = await db.execute(stmt)
                final_configurations = result.scalars().all()
                updated_count = len(
                    [
                        c
                        for c in to_potentially_update
                        if (
                            current_configs_dict[c].name != new_configs_dict[c].name
                            or current_configs_dict[c].description
                            != new_configs_dict[c].description
                            or current_configs_dict[c].value
                            != new_configs_dict[c].value
                        )
                    ]
                )

                self.logger.info(
                    f"Successfully synchronized {len(to_create)} created, "
                    f"{updated_count} updated, "
                    f"{len(to_delete)} deleted configurations for agent {agent_id}"
                )

                return [
                    AgentConfigurationResponse.model_validate(config)
                    for config in final_configurations
                ]

            except IntegrityError as e:
                self.logger.warning(
                    f"Integrity error synchronizing configurations: {e}"
                )
                await db.rollback()
                raise HTTPException(
                    status_code=409,
                    detail=f"Configuration synchronization failed due to "
                    f"constraint violation.",
                ) from e
            except Exception as e:
                self.logger.error(
                    f"Unexpected error in sync_agent_configurations: {e}",
                    exc_info=True,
                )
                await db.rollback()
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

    def get_router(self) -> APIRouter:
        """Get the configured router."""
        return self.router
