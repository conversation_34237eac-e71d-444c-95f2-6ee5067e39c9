"""Main entrypoint for migrations of all packages composing the Agent Manager."""

from logging.config import fileConfig

from gen_os_am_conversational.models.models import Base as ConversationalBase
from gen_os_am_knowledge.sdk.models import Base as KnowledgeBase
from gen_os_am_workflows.database.models import Base as WorkflowBase
from gen_os_sdk_emulator.core.database.session import SessionManager

from alembic import context
from gen_os_am_core.database.models.base import Base as CoreBase
from gen_os_am_core.settings import DatabaseSettings

db_settings = DatabaseSettings.get_settings()

target_metadata = [
    CoreBase.metadata,
    WorkflowBase.metadata,
    KnowledgeBase.metadata,
    ConversationalBase.metadata,
]

config = context.config


if config.config_file_name is not None:
    fileConfig(config.config_file_name)


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario, we need to create an Engine and associate a connection
    with the context.
    """
    with SessionManager.get_sync_session(db_settings).connection() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata, render_as_batch=True
        )

        with context.begin_transaction():
            context.run_migrations()

        connection.commit()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
