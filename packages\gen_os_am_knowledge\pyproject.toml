[project]
name = "gen-os-am-knowledge"
version = "0.1.1"

description = "Knowledge Management"

authors = [
    {name = "DareData, S.A.", email = "<EMAIL>"},
]

requires-python = ">=3.12"
readme = "README.md"
license = {text = "Proprietary"}
dependencies = [
    "fastapi>=0.115.12",
    "sqlalchemy>=2.0.37",
    "uvicorn>=0.30.0",
    "alembic>=1.15.2",
    "python-multipart>=0.0.20",
    "sqlmodel>=0.0.22",
    "regex>=2024.11.6",
    "pydantic>=2.10.6",
    "pathlib>=1.0.1",
    "psycopg[binary]>=3.2.4",
    "fsspec>=2025.2.0",
    "adlfs>=2024.12.0",
    "s3fs>=2025.2.0",
    "gcsfs>=2025.2.0",
    "httpx>=0.28.1",
    "aiobotocore==2.17.0", # due to issue the have in later versions https://github.com/fsspec/s3fs/issues/931
    "xxhash>=3.5.0",
    "contextvars>=2.4",
    "dotenv>=0.9.9",
    "pydantic-settings>=2.9.1",
    "greenlet>=3.2.1",
    "azure-storage-blob>=12.25.1",
    "opentelemetry-api>=1.30.0",
    "opentelemetry-sdk>=1.30.0",
    "opentelemetry-instrumentation-logging>=0.51b0",
    "ecs-logging>=2.2.0"
]


# this defines a local folder as a pipy source
# the filepath is temporary until we have a central repo
# other repos should have a similar one
[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[dependency-groups]
dev = [
    "black[jupyter]==24.8.0",
    "pre-commit<=4.0.0,>=3.8.0",
    "isort<6.0.0,>=5.13.2",
    "ipykernel<=7.0.0,>=6.29.5",
    "polylith-cli>=1.24.0",
    "alembic>=1.14.1",
    "ruff>=0.11.4",
]
test = ["pytest<9,>=8.3.3",
        "pytest-cov<7.0.0,>=6.0.0",
        "pytest-asyncio>=0.25.3",]
doc = [ "sphinx<9.0.0,>=8.0.0" ]

[tool.pdm.dev-dependencies]
gen-os = [
    "-e file:///${PROJECT_ROOT}/src/ingestions#egg=ctt-am-ingestions",
    "-e file:///${PROJECT_ROOT}/../gen_os_sdk_emulator#egg=gen-os-sdk-emulator"
]

[tool.ruff.lint]
select=[
    "I",   # isort
    "A",   # flake8-builtins
    "N",   # pep8-naming
    "D",   # pydocstyle
    "E",   # pycodestyle (Error)
    "YTT", # flake8-2020
    "B",   # flake8-bugbear
    "T10", # flake8-debugger
    "T20", # flake8-print
    "C4",  # flake8-comprehensions
]

ignore=[
    "D203", #0ne-blank-line-before-class
    "D213", #multi-line-summary-second-line
    "D104", #package docstrings
    "B008", # Allow function call in argument defaults (FastAPI Form/File pattern)
]

[tool.ruff.lint.per-file-ignores]
"tests/conftest.py" = ["E402"]


[tool.pdm]
distribution = true

[tool.pdm.scripts]
# alembic
alembic-generate = "sh -c 'cd src && alembic revision --autogenerate'"
alembic-migrate = "sh -c 'cd src && alembic upgrade head'"

# start a dev database
start-database="sh -c 'set -o allexport && . ./.env && set +o allexport && docker compose -f database/local.yml up -d'"
stop-database="sh -c 'docker compose -f database/local.yml down'"

# populate db with mock docs
populate-db = "sh -c 'cd database && python db_populate.py'"

# run with pdm run precommit
precommit = "sh -c 'pre-commit run --show-diff-on-failure --color=always --files $(git ls-files)'"

# clean
clean = "./scripts/clean.sh"

# run tests
tests = "sh -c 'pytest --cov --cov-branch && coverage report --omit=\"tests/*\"'"

# run api
api = "sh -c 'python src/gen_os_am_knowledge/api/main.py'"

#run alembic-migrate and api
service = "sh -c 'pdm run alembic-migrate && pdm run api'"

# TODO: We probably can remove the building of the wheels

build-ctt-am-ingestions = "sh -c 'pdm build -p ./src/ingestions && cp -r ./src/ingestions/dist/* shared/lib/'"
build-deps = {composite = ["build-ctt-am-ingestions"]}
