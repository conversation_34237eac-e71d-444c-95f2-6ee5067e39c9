"""Alembic environment configuration for database migrations.

This module configures the Alembic migration environment, including database connection
settings, logging, and migration execution modes. It supports both online and offline
migration modes and handles the configuration of the SQLAlchemy engine.
"""

from logging.config import fileConfig

from gen_os_sdk_emulator.core.database.session import SessionManager

from alembic import context
from gen_os_am_core.database.models.base import Base
from gen_os_am_core.settings import DatabaseSettings

db_settings = DatabaseSettings.get_settings()

config = context.config


if config.config_file_name is not None:
    fileConfig(config.config_file_name)


target_metadata = Base.metadata


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario, we need to create an Engine and associate a connection
    with the context.
    """
    with SessionManager.get_sync_session(db_settings).connection() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata, render_as_batch=True
        )

        with context.begin_transaction():
            context.run_migrations()
        connection.commit()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
