"""Basic tests for the core package."""

import pytest
from fastapi.testclient import Test<PERSON>lient


def test_healthz_endpoint(client: TestClient):
    """Test the healthz endpoint."""
    response = client.get("/healthz")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "ok"


def test_readyz_endpoint(client: TestClient):
    """Test the readyz endpoint."""
    response = client.get("/readyz")
    assert response.status_code in [200, 207]  # Can be 200 or 207 depending on services
    data = response.json()
    assert "status" in data
    assert "checks" in data
    assert "config" in data["checks"]
    assert "database" in data["checks"]
    assert "resources" in data["checks"]


def test_database_connection(get_session):
    """Test that database connection works."""
    # This test verifies that the database session can be created
    # The actual connection test would be done in an async test
    assert get_session is not None


def test_api_routes_exist(client: TestClient, create_test_agent):
    """Test that expected API routes exist."""
    # Test that the agent configuration routes are properly mounted
    test_agent_id = create_test_agent

    # This should return 200 for GET (list configurations)
    response = client.get(f"/agents/{test_agent_id}/core/v1/agent-configuration")
    assert response.status_code == 200

    # This should return 422 for PATCH without body (validation error)
    response = client.patch(f"/agents/{test_agent_id}/core/v1/agent-configuration")
    assert response.status_code == 422
