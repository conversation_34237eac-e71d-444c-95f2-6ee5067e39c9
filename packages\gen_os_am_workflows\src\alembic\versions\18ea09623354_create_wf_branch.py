"""create wf  branch.

Revision ID: 18ea09623354
Revises:
Create Date: 2025-07-16 14:07:27.405943

"""

from collections.abc import Sequence

# revision identifiers, used by Alembic.
revision: str = "18ea09623354"
down_revision: str | Sequence[str] | None = "5a46c993d343"
branch_labels: str | Sequence[str] | None = ("wf",)
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
